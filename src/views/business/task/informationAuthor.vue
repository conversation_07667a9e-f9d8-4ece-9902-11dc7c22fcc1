<template>
  <div id="star-layout-body-content">
    <div id="layout-content">
      <div id="publish-reconstruct">
        <div class="module-primary-box">
          <div class="module-primary-box">
            <!-- <p style="font-size: 18px; font-weight: bold; padding: 0 0 5px 5px">下单信息填写</p> -->
            <div class="publish-reconstruct module-primary-group">
              <div id="project" class="module-primary">
                <div class="module-primary-legend">
                  基础信息
                  <div class="module-primary-action">
                    <el-button
                      @click="createTask"
                      :disabled="page_type == 'info'"
                      v-if="route?.query?.path == 'talentList'"
                      type="primary"
                      size="small"
                    >
                      创建新任务
                    </el-button>
                  </div>
                </div>
                <div class="module-secondary-group">
                  <!-- 基础信息骨架屏 -->
                  <div v-if="pageLoading" class="basic-info-skeleton">
                    <el-skeleton :rows="6" animated>
                      <template #template>
                        <div class="skeleton-info-grid">
                          <div class="skeleton-info-item" v-for="i in 12" :key="i">
                            <el-skeleton-item variant="text" style="width: 80px; margin-bottom: 8px;" />
                            <el-skeleton-item variant="text" style="width: 200px;" />
                          </div>
                        </div>
                      </template>
                    </el-skeleton>
                  </div>

                  <!-- 实际的基础信息内容 -->
                  <div v-else class="basic-info-grid">
                    <div class="info-item">
                      <div class="info-label">任务信息</div>
                      <div class="info-value">{{ taskDetail?.task?.task_name + '（ID:' + taskDetail?.task?.id + '）' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">项目信息</div>
                      <div class="info-value">{{ taskDetail?.project?.project_name + '（ID:' + taskDetail?.project?.id + '）' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">平台</div>
                      <div class="info-value">{{ platform[taskDetail?.task?.platform_type] }}</div>
                    </div>

                    <!-- 推广内容 - 仅在平台为抖音时展示 -->
                    <div class="info-item" v-if="taskDetail?.task?.platform_type === 1 || taskDetail?.task?.platform_type === 2">
                      <div class="info-label">推广内容</div>
                      <div class="info-value">{{ taskDetail?.project?.promote_type == 1 ? '星立方' : '其他' }}</div>
                    </div>

                    <!-- 任务模式 - 仅在抖音和B站展示 -->
                    <div class="info-item" v-if="taskDetail?.task?.platform_type === 1 || taskDetail?.task?.platform_type === 2 || taskDetail?.task?.platform_type === 5">
                      <div class="info-label">任务模式</div>
                      <div class="info-value">
                        <!-- 抖音平台显示指派/招募/推广 -->
                        <template v-if="taskDetail?.task?.platform_type === 1 || taskDetail?.task?.platform_type === 2">
                          {{ taskModeDouyin[taskDetail?.task?.task_type] || '--' }}
                        </template>
                        <!-- B站显示京火/花火 -->
                        <template v-else-if="taskDetail?.task?.platform_type === 5">
                          {{ taskModeBilibili[taskDetail?.task?.task_type] || '--' }}
                        </template>
                      </div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">发起人</div>
                      <div class="info-value">{{ taskDetail?.task?.created_name }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">发起人部门</div>
                      <div class="info-value">{{ taskDetail?.task?.department_name }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">业绩归属人</div>
                      <div class="info-value">{{ taskDetail?.process_before?.operator }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">业绩归属人部门</div>
                      <div class="info-value">{{ taskDetail?.process_before?.department }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">客户名称</div>
                      <div class="info-value">{{ taskDetail?.project?.custom_company }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">任务创建时间</div>
                      <div class="info-value">{{ taskDetail?.task?.created_at }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">下单方式</div>
                      <div class="info-value">{{ taskDetail?.task?.order_method == 1 ? '星推下单' : '线下下单' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">订单类型</div>
                      <div class="info-value">{{ taskDetail?.task?.order_type == 1 ? '自运营' : taskDetail?.task?.order_type == 2 ? '走单（不含代理服务费）' : taskDetail?.task?.order_type == 3 ? '代下单（含代理服务费）' : taskDetail?.task?.order_type == 4 ? '资源包订单' : '水下订单' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">账户信息</div>
                      <div class="info-value">{{ taskDetail?.task?.account_name || '--' }}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">合同信息</div>
                      <div class="info-value">{{ taskDetail?.process_before?.customer_contract}}</div>
                    </div>
                    <div class="info-item">
                      <div class="info-label">充值合同信息</div>
                      <div class="info-value">{{ taskDetail?.task?.recharge_contract_id + '(¥' + taskDetail?.task?.oa_recharge_amount + ')'}}</div>
                    </div>
                  </div>

                </div>
              </div>

              <!-- 招募任务模式专用卡片 -->
              <!-- 总利润信息卡片 - 仅在平台为抖音、任务模式为招募时展示 -->
              <div id="profit-info" class="module-primary" v-if="(taskDetail?.task?.platform_type === 1 || taskDetail?.task?.platform_type === 2) && taskDetail?.task?.task_type === 2">
                <div class="module-primary-legend">
                  总利润信息
                </div>
                <div class="module-secondary-group">
                  <div v-if="pageLoading" class="basic-info-skeleton">
                    <el-skeleton :rows="3" animated>
                      <template #template>
                        <div class="skeleton-info-grid">
                          <div class="skeleton-info-item" v-for="i in 4" :key="i">
                            <el-skeleton-item variant="text" style="width: 80px; margin-bottom: 8px;" />
                            <el-skeleton-item variant="text" style="width: 200px;" />
                          </div>
                        </div>
                      </template>
                    </el-skeleton>
                  </div>
                  <div v-else>
                    <el-form :model="recruitmentForm" :rules="recruitmentRules" ref="profitFormRef" label-width="120px" class="recruitment-form">
                      <div class="form-row-single">
                        <el-form-item label="任务预估总预算" prop="assess_total_budget" required class="form-item-quarter">
                          <el-input
                            v-model.number="recruitmentForm.assess_total_budget"
                            type="number"
                            placeholder="请输入预算"
                            :disabled="page_type === 'info' || page_type === 'change'"
                            @input="calculateProfitInfo"
                          >
                            <template #suffix>元</template>
                          </el-input>
                        </el-form-item>
                        <el-form-item label="期望合作达人数" prop="expect_coop_num" required class="form-item-quarter">
                          <el-input
                            v-model.number="recruitmentForm.expect_coop_num"
                            type="number"
                            placeholder="请输入人数"
                            :disabled="page_type === 'info' || page_type === 'change'"
                            @input="calculateProfitInfo"
                          >
                            <template #suffix>人</template>
                          </el-input>
                        </el-form-item>
                        <el-form-item label="预估总毛利" prop="assess_total_gross" required class="form-item-quarter">
                          <el-input
                            v-model.number="recruitmentForm.assess_total_gross"
                            type="number"
                            placeholder="请输入毛利"
                            :disabled="page_type === 'info' || page_type === 'change'"
                            @input="calculateProfitMargin"
                          >
                            <template #suffix>元</template>
                          </el-input>
                        </el-form-item>
                        <el-form-item label="预估总毛利率" prop="assess_total_gross_rate" required class="form-item-quarter">
                          <el-input
                            v-model.number="recruitmentForm.assess_total_gross_rate"
                            type="number"
                            placeholder="请输入毛利率"
                            :disabled="page_type === 'info' || page_type === 'change'"
                            @input="calculateProfitAmount"
                          >
                            <template #suffix>%</template>
                          </el-input>
                        </el-form-item>
                      </div>
                    </el-form>
                  </div>
                </div>
              </div>

              <!-- 结算方式卡片 - 仅在平台为抖音、任务模式为招募时展示 -->
              <div id="settlement-method" class="module-primary" v-if="(taskDetail?.task?.platform_type === 1 || taskDetail?.task?.platform_type === 2) && taskDetail?.task?.task_type === 2">
                <div class="module-primary-legend">
                  结算方式
                  <el-button @click="()=>exportKolList(true)" type="primary"> 导出全部信息 </el-button>
                </div>
                <div class="module-secondary-group">
                  <div v-if="pageLoading" class="basic-info-skeleton">
                    <el-skeleton :rows="4" animated>
                      <template #template>
                        <div class="skeleton-info-grid">
                          <div class="skeleton-info-item" v-for="i in 12" :key="i">
                            <el-skeleton-item variant="text" style="width: 80px; margin-bottom: 8px;" />
                            <el-skeleton-item variant="text" style="width: 200px;" />
                          </div>
                        </div>
                      </template>
                    </el-skeleton>
                  </div>
                  <div v-else>
                    <el-form :model="recruitmentForm" :rules="recruitmentRules" ref="settlementFormRef" label-width="140px" class="recruitment-form">
                      <!-- 第一行：结算方式、招募形式、选择达人团、保底模式 -->
                      <div class="form-row-single">
                        <el-form-item label="结算方式" prop="settlement_method" required class="form-item-quarter">
                          <el-select
                            v-model="recruitmentForm.settlement_method"
                            placeholder="请选择结算方式"
                            :disabled="page_type === 'info' || page_type === 'change'"
                            style="width: 100%"
                          >
                            <el-option label="按一口价结算" :value="1" />
                            <el-option label="按自然播放量结算" :value="3" />
                          </el-select>
                        </el-form-item>
                        <el-form-item label="招募形式" prop="recruit_type" :required="!settlementFieldsDisabled.recruit_type" class="form-item-quarter">
                          <el-select
                            v-model="recruitmentForm.recruit_type"
                            placeholder="请选择招募形式"
                            :disabled="settlementFieldsDisabled.recruit_type"
                            style="width: 100%"
                          >
                            <el-option label="服务商承包" value="服务商承包" />
                            <el-option label="自助招募" value="自助招募" />
                          </el-select>
                        </el-form-item>
                        <el-form-item label="选择达人团" prop="selected_kol_type" class="form-item-quarter">
                          <el-select
                            v-model="recruitmentForm.selected_kol_type"
                            placeholder="请选择达人团"
                            :disabled="settlementFieldsDisabled.selected_kol_type"
                            style="width: 100%"
                            clearable
                          >
                            <el-option label="种草达人团" value="种草达人团" />
                            <el-option label="带货达人团" value="带货达人团" />
                            <el-option label="话题达人团" value="话题达人团" />
                          </el-select>
                        </el-form-item>
                        <el-form-item label="保底模式" prop="bottom_mode" class="form-item-quarter">
                          <el-select
                            v-model="recruitmentForm.bottom_mode"
                            placeholder="请选择保底模式"
                            :disabled="settlementFieldsDisabled.bottom_mode"
                            style="width: 100%"
                            clearable
                          >
                            <el-option label="有保底" value="有保底" />
                            <el-option label="无保底" value="无保底" />
                          </el-select>
                        </el-form-item>
                      </div>

                      <!-- 第二行：考核指标、达人保底价、达人一口价、CPM单价 -->
                      <div class="form-row-single">
                        <el-form-item label="考核指标" prop="assess_indicator" :required="!settlementFieldsDisabled.assess_indicator" class="form-item-quarter">
                          <el-select
                            v-model="recruitmentForm.assess_indicator"
                            placeholder="请选择考核指标"
                            :disabled="settlementFieldsDisabled.assess_indicator"
                            style="width: 100%"
                          >
                            <el-option label="视频自然播放量" value="视频自然播放量" />
                          </el-select>
                        </el-form-item>
                        <el-form-item label="达人保底价" prop="kol_bottom_min_price" class="form-item-quarter">
                          <div class="price-range-input">
                            <el-input
                              v-model.number="recruitmentForm.kol_bottom_min_price"
                              type="number"
                              placeholder="最低价"
                              :disabled="settlementFieldsDisabled.kol_bottom_min_price"
                              style="width: 45%"
                            />
                            <span style="margin: 0 5px;">-</span>
                            <el-input
                              v-model.number="recruitmentForm.kol_bottom_max_price"
                              type="number"
                              placeholder="最高价"
                              :disabled="settlementFieldsDisabled.kol_bottom_min_price"
                              style="width: 45%"
                            />
                          </div>
                        </el-form-item>
                        <el-form-item label="达人一口价" prop="kol_min_price" class="form-item-quarter">
                          <div class="price-range-input">
                            <el-input
                              v-model.number="recruitmentForm.kol_min_price"
                              type="number"
                              placeholder="最低价"
                              :disabled="settlementFieldsDisabled.kol_min_price"
                              style="width: 45%"
                            />
                            <span style="margin: 0 5px;">-</span>
                            <el-input
                              v-model.number="recruitmentForm.kol_max_price"
                              type="number"
                              placeholder="最高价"
                              :disabled="settlementFieldsDisabled.kol_min_price"
                              style="width: 45%"
                            />
                          </div>
                        </el-form-item>
                        <el-form-item label="CPM单价" prop="cpm_price" class="form-item-quarter">
                          <el-input
                            v-model.number="recruitmentForm.cpm_price"
                            type="number"
                            placeholder="请输入CPM单价"
                            :disabled="settlementFieldsDisabled.cpm_price"
                          >
                            <template #suffix>元</template>
                          </el-input>
                        </el-form-item>
                      </div>

                      <!-- 第三行：DOU+流量是否计费、服务商选择方式、服务商返点比例、服务商返点金额 -->
                      <div class="form-row-single">
                        <el-form-item label="DOU+流量是否计费" prop="dou_is_cal_price" class="form-item-quarter">
                          <el-select
                            v-model="recruitmentForm.dou_is_cal_price"
                            placeholder="请选择"
                            :disabled="settlementFieldsDisabled.dou_is_cal_price"
                            style="width: 100%"
                            clearable
                          >
                            <el-option label="是" value="是" />
                            <el-option label="否" value="否" />
                          </el-select>
                        </el-form-item>
                        <el-form-item label="服务商选择方式" prop="service_select_mode" class="form-item-quarter">
                          <el-select
                            v-model="recruitmentForm.service_select_mode"
                            placeholder="请选择"
                            :disabled="settlementFieldsDisabled.service_select_mode"
                            style="width: 100%"
                            clearable
                          >
                            <el-option label="是" value="是" />
                            <el-option label="否" value="否" />
                          </el-select>
                        </el-form-item>
                        <el-form-item label="服务商返点比例" prop="service_rebate_rate" class="form-item-quarter">
                          <el-input
                            v-model.number="recruitmentForm.service_rebate_rate"
                            type="number"
                            placeholder="请输入返点比例"
                            :disabled="page_type === 'info' || page_type === 'change'"
                          >
                            <template #suffix>%</template>
                          </el-input>
                        </el-form-item>
                      </div>
                    </el-form>
                  </div>
                </div>
              </div>

              <!-- 任务要求卡片 - 仅在平台为抖音、任务模式为招募时展示 -->
              <div id="task-requirements" class="module-primary" v-if="(taskDetail?.task?.platform_type === 1 || taskDetail?.task?.platform_type === 2) && taskDetail?.task?.task_type === 2">
                <div class="module-primary-legend">
                  任务要求
                </div>
                <div class="module-secondary-group">
                  <div v-if="pageLoading" class="basic-info-skeleton">
                    <el-skeleton :rows="2" animated>
                      <template #template>
                        <div class="skeleton-info-grid">
                          <div class="skeleton-info-item" v-for="i in 4" :key="i">
                            <el-skeleton-item variant="text" style="width: 80px; margin-bottom: 8px;" />
                            <el-skeleton-item variant="text" style="width: 200px;" />
                          </div>
                        </div>
                      </template>
                    </el-skeleton>
                  </div>
                  <div v-else>
                    <el-form :model="recruitmentForm" :rules="recruitmentRules" ref="taskRequirementsFormRef" label-width="140px" class="recruitment-form">
                      <!-- 第一行：镜头要求 -->
                      <div>
                        <el-form-item label="镜头要求" prop="lens_requirement" required class="form-item-quarter">
                          <el-input
                            v-model="recruitmentForm.lens_requirement"
                            type="textarea"
                            :rows="2"
                            placeholder="请输入镜头要求，不超过300字"
                            :disabled="page_type === 'info' || page_type === 'change'"
                            maxlength="300"
                            show-word-limit
                            resize="vertical"
                          />
                        </el-form-item>
                      </div>
                      <!-- 第二行：参考素材 -->
                      <div class="form-row-single">
                        <el-form-item label="参考素材" prop="reference_material" required class="form-item-full">
                          <el-upload
                            v-model:file-list="referenceMaterialFileList"
                            :http-request="uploadReferenceMaterialRequest"
                            list-type="picture-card"
                            :disabled="page_type === 'info' || page_type === 'change'"
                            accept="image/*"
                            multiple
                            :limit="10"
                            :on-preview="handlePreview"
                            :on-remove="handleReferenceMaterialRemove"
                          >
                            <el-icon><Plus /></el-icon>
                          </el-upload>
                          <div class="upload-tip">支持上传图片格式，最多10张</div>
                        </el-form-item>
                      </div>

                      <!-- 第三行：报名日期、期望保留时长 -->
                      <div class="form-row-single">
                        <el-form-item label="报名日期" prop="task_enroll_date" required class="form-item-half">
                          <el-date-picker
                            v-model="recruitmentForm.task_enroll_date"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :disabled="page_type === 'info' || page_type === 'change'"
                            style="width: 100%"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                          />
                        </el-form-item>
                        <el-form-item label="期望保留时长" prop="expect_save_time" required class="form-item-half">
                          <el-input
                            v-model.number="recruitmentForm.expect_save_time"
                            type="number"
                            placeholder="请输入保留天数"
                            :disabled="page_type === 'info' || page_type === 'change'"
                          >
                            <template #suffix>天</template>
                          </el-input>
                        </el-form-item>
                      </div>
                    </el-form>
                  </div>
                </div>
              </div>

              <!-- 任务信息卡片 - 仅在平台为抖音、任务模式为招募时展示 -->
              <div id="task-info" class="module-primary" v-if="(taskDetail?.task?.platform_type === 1 || taskDetail?.task?.platform_type === 2) && taskDetail?.task?.task_type === 2">
                <div class="module-primary-legend">
                  任务信息
                </div>
                <div class="module-secondary-group">
                  <div v-if="pageLoading" class="basic-info-skeleton">
                    <el-skeleton :rows="4" animated>
                      <template #template>
                        <div class="skeleton-info-grid">
                          <div class="skeleton-info-item" v-for="i in 7" :key="i">
                            <el-skeleton-item variant="text" style="width: 80px; margin-bottom: 8px;" />
                            <el-skeleton-item variant="text" style="width: 200px;" />
                          </div>
                        </div>
                      </template>
                    </el-skeleton>
                  </div>
                  <div v-else>
                    <el-form :model="recruitmentForm" :rules="recruitmentRules" ref="taskInfoFormRef" label-width="140px" class="recruitment-form">
                      <!-- 第一行：任务名称、达人侧任务名称 -->
                      <div class="form-row-single">
                        <!-- <el-form-item label="任务名称" prop="taskName" required class="form-item-half">
                          <el-input
                            v-model="recruitmentForm.taskName"
                            placeholder="请输入任务名称"
                            :disabled="page_type === 'info' || page_type === 'change'"
                            maxlength="50"
                            show-word-limit
                          />
                        </el-form-item> -->
                        <el-form-item label="达人侧任务名称" prop="kol_task_name" required class="form-item-half">
                          <el-input
                            v-model="recruitmentForm.kol_task_name"
                            placeholder="请输入达人侧任务名称"
                            :disabled="page_type === 'info' || page_type === 'change'"
                            maxlength="50"
                            show-word-limit
                          />
                        </el-form-item>
                      </div>

                      <!-- 第二行：任务图标 -->
                      <div class="form-row-single">
                        <el-form-item label="任务图标" prop="task_icon" required class="form-item-full">
                          <el-upload
                            v-model:file-list="taskIconFileList"
                            :http-request="uploadTaskIconRequest"
                            list-type="picture-card"
                            :disabled="page_type === 'info' || page_type === 'change'"
                            accept="image/*"
                            :limit="1"
                            :on-preview="handlePreview"
                            :on-remove="handleTaskIconRemove"
                          >
                            <el-icon><Plus /></el-icon>
                          </el-upload>
                          <div class="upload-tip">支持上传图片格式，仅限1张</div>
                        </el-form-item>
                      </div>

                      <!-- 第三行：星图活动IP、产品名称 -->
                      <div class="form-row-single">
                        <el-form-item label="星图活动IP" prop="xingtu_activity_ip" class="form-item-half">
                          <el-input
                            v-model="recruitmentForm.xingtu_activity_ip"
                            placeholder="请输入ID+活动名称"
                            :disabled="page_type === 'info' || page_type === 'change'"
                          />
                        </el-form-item>
                        <el-form-item label="产品名称" prop="product_name" required class="form-item-half">
                          <el-input
                            v-model="recruitmentForm.product_name"
                            placeholder="请输入产品名称"
                            :disabled="page_type === 'info' || page_type === 'change'"
                            maxlength="40"
                            show-word-limit
                          />
                        </el-form-item>
                      </div>

                      <!-- 第四行：产品介绍 -->
                      <div>
                        <el-form-item label="产品介绍" prop="product_desc" required class="form-item-quarter">
                          <el-input
                            v-model="recruitmentForm.product_desc"
                            type="textarea"
                            :rows="2"
                            placeholder="请输入产品介绍"
                            :disabled="page_type === 'info' || page_type === 'change'"
                            maxlength="1000"
                            show-word-limit
                            resize="vertical"
                          />
                        </el-form-item>
                      </div>

                      <!-- 第五行：商品链接 -->
                      <div class="form-row-single">
                        <el-form-item label="商品链接" prop="product_link" class="form-item-full">
                          <el-input
                            v-model="recruitmentForm.product_link"
                            placeholder="请输入商品链接"
                            :disabled="page_type === 'info' || page_type === 'change'"
                          />
                        </el-form-item>
                      </div>
                    </el-form>
                  </div>
                </div>
              </div>

              <!-- 组件信息卡片 - 仅在平台为抖音、任务模式为招募时展示 -->
              <div id="component-info" class="module-primary" v-if="(taskDetail?.task?.platform_type === 1 || taskDetail?.task?.platform_type === 2) && taskDetail?.task?.task_type === 2">
                <div class="module-primary-legend">
                  组件信息
                </div>
                <div class="module-secondary-group">
                  <div v-if="pageLoading" class="basic-info-skeleton">
                    <el-skeleton :rows="4" animated>
                      <template #template>
                        <div class="skeleton-info-grid">
                          <div class="skeleton-info-item" v-for="i in 8" :key="i">
                            <el-skeleton-item variant="text" style="width: 80px; margin-bottom: 8px;" />
                            <el-skeleton-item variant="text" style="width: 200px;" />
                          </div>
                        </div>
                      </template>
                    </el-skeleton>
                  </div>
                  <div v-else>
                    <el-form :model="componentForm" :rules="componentRules" ref="componentFormRef" label-width="140px" class="recruitment-form">
                      <!-- 第二行：是否投放广告 -->
                      <div class="form-row-single">
                        <el-form-item label="是否投放广告" prop="isPublishAdv" class="form-item-full">
                          <el-checkbox-group v-model="componentForm.is_publish_adv" :disabled="page_type === 'info' || page_type === 'change'">
                            <el-checkbox label="1">投放效果广告</el-checkbox>
                            <el-checkbox label="2">投放品牌广告</el-checkbox>
                            <el-checkbox label="3">投放巨量千川</el-checkbox>
                            <el-checkbox label="4">投放至Dou+</el-checkbox>
                          </el-checkbox-group>
                        </el-form-item>
                      </div>

                      <!-- 第三行：广告主ID字段 -->
                      <div class="form-row-single">
                        <el-form-item
                          label="巨量广告主ID-效果广告"
                          prop="effect_adv_id"
                          class="form-item-half"
                          :required="componentForm.is_publish_adv.includes('1')"
                        >
                          <el-input
                            v-model="componentForm.effect_adv_id"
                            :disabled="page_type === 'info' || page_type === 'change' || !componentForm.is_publish_adv.includes('1')"
                            placeholder="请输入广告主ID"
                          />
                        </el-form-item>
                        <el-form-item
                          label="巨量广告主ID-品牌广告"
                          prop="brand_adv_id"
                          class="form-item-half"
                          :required="componentForm.is_publish_adv.includes('2')"
                        >
                          <el-input
                            v-model="componentForm.brand_adv_id"
                            :disabled="page_type === 'info' || page_type === 'change' || !componentForm.is_publish_adv.includes('2')"
                            placeholder="请输入广告主ID"
                          />
                        </el-form-item>
                        <el-form-item
                          label="巨量千川广告主ID"
                          prop="qianchuan_adv_id"
                          class="form-item-half"
                          :required="componentForm.is_publish_adv.includes('3')"
                        >
                          <el-input
                            v-model="componentForm.qianchuan_adv_id"
                            :disabled="page_type === 'info' || page_type === 'change' || !componentForm.is_publish_adv.includes('3')"
                            placeholder="请输入广告主ID"
                          />
                        </el-form-item>
                      </div>

                      <!-- 第四行：千川和Dou+广告主ID -->
                      <div class="form-row-single">
                        
                        <el-form-item
                          label="Dou+广告主ID"
                          prop="dou_adv_id"
                          class="form-item-half"
                          :required="componentForm.is_publish_adv.includes('4')"
                        >
                          <el-input
                            v-model="componentForm.dou_adv_id"
                            :disabled="page_type === 'info' || page_type === 'change' || !componentForm.is_publish_adv.includes('4')"
                            placeholder="请输入广告主ID"
                          />
                        </el-form-item>
                        <el-form-item
                          label="Dou+广告主抖音UID"
                          prop="dou_adv_uid"
                          class="form-item-half"
                          :required="componentForm.is_publish_adv.includes('4')"
                        >
                          <el-input
                            v-model="componentForm.dou_adv_uid"
                            :disabled="page_type === 'info' || page_type === 'change' || !componentForm.is_publish_adv.includes('4')"
                            placeholder="请输入抖音UID"
                          />
                        </el-form-item>
                      </div>

                      <!-- 第五行：Dou+抖音UID和组件名称 -->
                      <div class="form-row-single">
                        
                        <el-form-item label="组件名称" prop="component_type" class="form-item-half">
                          <el-select
                            v-model="componentForm.component_type"
                            placeholder="请选择组件"
                            :disabled="page_type === 'info' || page_type === 'change'"
                            style="width: 167px"
                          >
                            <el-option label="购物车" :value="1" />
                            <el-option label="落地页" :value="2" />
                            <el-option label="搜索组件" :value="3" />
                            <el-option label="其他" :value="4" />
                          </el-select>
                        </el-form-item>

                        <el-form-item label="组件文案" prop="component_content" class="form-item-full">
                          <el-input
                            v-model="componentForm.component_content"
                            placeholder="请输入组件文案"
                            :disabled="page_type === 'info' || page_type === 'change'"
                          />
                        </el-form-item>
                        <el-form-item label="组件链接" prop="component_url" class="form-item-full">
                          <el-input
                            v-model="componentForm.component_url"
                            placeholder="请输入组件链接"
                            :disabled="page_type === 'info' || page_type === 'change'"
                          />
                        </el-form-item>
                      </div>

                      <!-- 第八行：组件备注 -->
                      <div>
                        <el-form-item label="组件备注" prop="component_remark" class="form-item-full">
                          <el-input
                            type="textarea"
                            v-model="componentForm.component_remark"
                            rows="2"
                            placeholder="请输入组件备注"
                            :disabled="page_type === 'info' || page_type === 'change'"
                          />
                        </el-form-item>
                      </div>

                      <!-- 第九行：其他特殊备注 -->
                      <div>
                        <el-form-item label="其他特殊备注" prop="extra_remark" class="form-item-full">
                          <el-input
                            type="textarea"
                            v-model="componentForm.extra_remark"
                            rows="2"
                            placeholder="请输入其他特殊备注（如有）"
                            :disabled="page_type === 'info' || page_type === 'change'"
                          />
                        </el-form-item>
                      </div>
                    </el-form>
                  </div>
                </div>
              </div>

              <div id="category" class="module-primary">
                <div class="module-primary-legend">
                  达人信息
                  <span>
                    <el-button @click="exportKolList" v-show="page_type == 'info'"> 导出达人 </el-button>
                    <el-button @click="openDialogInformation" :disabled="page_type == 'info'"> 批量设置建联媒介 </el-button>
                    <el-button @click="addKolHandle" :disabled="page_type == 'info'"> 添加达人 </el-button>
                    <el-button
                      @click="dialogTaskVisible = true"
                      v-if="route.query.id || taskDetail?.task?.id || task_id"
                      :disabled="page_type == 'info'"
                    >
                      导入达人
                    </el-button>
                    <el-button
                      @click="getKolPriceHandle"
                      :disabled="page_type == 'info'"
                      v-if="taskDetail?.task?.platform_type === 1 || taskDetail?.task?.platform_type === 2"
                    >
                      获取实时报价
                    </el-button>
                    <!-- <el-button @click="createReportHandle" type="primary" :disabled="page_type == 'info'">
                      创建提报任务
                    </el-button> -->
                  </span>
                </div>

                

                <div class="module-secondary-group">
                  <!-- 达人信息骨架屏 -->
                  <div v-if="pageLoading" class="talent-info-skeleton">
                    <div class="talent-summary-skeleton">
                      <el-skeleton-item variant="text" style="width: 300px; height: 20px; margin-bottom: 10px;" />
                    </div>
                    <el-skeleton :rows="5" animated>
                      <template #template>
                        <div class="skeleton-table">
                          <div class="skeleton-table-header">
                            <el-skeleton-item variant="rect" style="width: 100%; height: 40px; margin-bottom: 10px;" />
                          </div>
                          <div class="skeleton-table-rows">
                            <div class="skeleton-table-row" v-for="i in 3" :key="i">
                              <el-skeleton-item variant="circle" style="width: 45px; height: 45px; margin-right: 16px;" />
                              <div class="skeleton-table-cell">
                                <el-skeleton-item variant="text" style="width: 120px; margin-bottom: 4px;" />
                                <el-skeleton-item variant="text" style="width: 80px;" />
                              </div>
                              <el-skeleton-item variant="text" style="width: 100px; margin: 0 16px;" />
                              <el-skeleton-item variant="text" style="width: 80px; margin: 0 16px;" />
                              <el-skeleton-item variant="text" style="width: 100px; margin: 0 16px;" />
                              <el-skeleton-item variant="text" style="width: 120px; margin: 0 16px;" />
                            </div>
                          </div>
                        </div>
                      </template>
                    </el-skeleton>
                  </div>

                  <!-- 实际的达人信息内容 -->
                  <div v-else>
                    <p v-if="(taskDetail.task.platform_type==1 || taskDetail.task.platform_type==2) && taskDetail.task.task_type == 2" class="font-semibold" style="background-color: #fff;padding: 0 0 0 10px;">已选达人 {{ kolList?.length }}</p>
                    <p v-else class="font-semibold" style="background-color: #fff;padding: 0 0 0 10px;">已选达人 {{ kolList?.length }} &nbsp;&nbsp;&nbsp;&nbsp; 总毛利率：{{ page_type == 'info' ? taskDetail?.task?.total_gross : totalGrossMargin }}{{ page_type == 'info' ? '%' : '' }}</p>
                    <!-- 搜索框 -->
                    <div class="talent-search-container" v-if="!pageLoading && selectionKolList && selectionKolList?.length > 0">
                      <el-input
                        v-model="talentSearchKeyword"
                        placeholder="搜索达人昵称或星图ID"
                        clearable
                        prefix-icon="Search"
                        style="width: 300px; margin: 10px 0;"
                        @input="handleTalentSearch"
                        @clear="handleTalentSearch"
                      />
                      <span v-if="talentSearchKeyword" class="search-result-text">
                        找到 {{ filteredKolList.length }} 个结果
                      </span>
                    </div>
                    <div v-if="selectionKolList && selectionKolList?.length > 0">
                      <div style="height: 550px; overflow: auto;border: 1px solid #ebeef5;">
                        <el-auto-resizer>
                          <template #default="{ height, width }">
                            <el-table-v2
                              ref="kolListRef"
                              :columns="tableColumns"
                              :data="filteredKolList"
                              :width="width"
                              :height="height"
                              :row-class="tableRowClassName"
                              :header-height="50"
                              :row-height="80"
                              :cache-size="10"
                              fixed
                            />
                          </template>
                        </el-auto-resizer>
                      </div>


                    </div>
                    <div v-else>
                      <el-empty description="暂无已选达人"> </el-empty>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 新增审核记录板块 -->
              <div id="audit-records" class="module-primary" v-if="page_type == 'info'">
                <div class="module-primary-legend">
                  <div>审核记录</div>
                </div>
                <div class="module-secondary-group">
                  <div class="audit-timeline" v-if="auditRecords.length > 0">
                    <div class="audit-item" v-for="(review, index) in auditRecords" :key="index" :class="{'audit-item-success': review.reviewer_status === 1, 'audit-item-error': review.reviewer_status !== 1}">
                      <div class="audit-marker">
                        <div class="audit-icon">
                          <el-icon v-if="review.reviewer_status === 1"><Check /></el-icon>
                          <el-icon v-else><Close /></el-icon>
                        </div>
                        <div class="audit-line" v-if="index !== auditRecords.length - 1"></div>
                      </div>
                      <div class="audit-content">
                        <div class="audit-header">
                          <div class="audit-reviewer">
                            <span class="reviewer-name">{{ review.reviewer_personnel || '-' }}</span>
                            <el-tag size="small" :type="review.reviewer_status === 1 ? 'success' : 'danger'" class="status-tag">
                              {{ review.reviewer_status === 1 ? '审核通过' : '驳回' }}
                            </el-tag>
                          </div>
                          <div class="audit-time">{{ review.created_at || '-' }}</div>
                        </div>
                        <div class="audit-body">
                          <p class="audit-comment">{{ review.reviewer_comments || '无审核意见' }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <el-empty v-else description="暂无审核记录"></el-empty>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="view-footer" data-btm="c8297">
          <div class="footer-info"></div>
          <div class="footer-action">
            <el-button @click="cancelWriteOrder" :disabled="submitButtonLoadding"> 取消 </el-button>
            <!-- 保存和提交按钮 - 在非info和非change模式下显示 -->
            <el-button
              type="primary"
              @click="addTaskDataStatusFun(1)"
              :disabled="(page_type == 'info' || (page_type == 'edit' && taskDetail?.task?.status == 3)) || submitButtonLoadding"
              v-show="page_type != 'info' && page_type != 'change'"
            >
              保存
            </el-button>
            <el-button
              type="primary"
              @click="addTaskDataStatusFun(2)"
              :disabled="page_type == 'info' || submitButtonLoadding"
              v-show="page_type != 'info' && page_type != 'change'"
            >
              提交
            </el-button>
            <!-- 变更按钮 - 在change模式下显示 -->
            <el-button
              type="primary"
              @click="addTaskDataStatusFun(3)"
              :disabled="submitButtonLoadding"
              v-show="page_type == 'change'"
            >
              变更
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <addKolDialog :dialog-visible="addKolDialogVisible" @cancel-add-kol="cancelAddKol" @confirm-add-kol="confirmAddKol">
    </addKolDialog>
    <submitReport :dialog-visible="addSubmitReportVisible" @cancel-add-kol="cancelAddKol" :kol-select-confirm="kolSelectConfirm">
    </submitReport>
    <unifiedKolDialog
      v-model:dialogVisible="dialogVisible"
      :dialogType="dialogType"
      :rowData="rowItem"
      :mediumOptions="medium"
      :mcnList="mcnList.list || []"
      :platformType="taskDetail?.task?.platform_type || 1"
      :orderMethod="taskDetail?.task?.order_method || 1"
      :orderType="taskDetail?.task?.order_type"
      :taskType="taskDetail?.task?.task_type"
      @cancel="cancelWrite"
      @confirm="confirmAddKolInfo"
    ></unifiedKolDialog>
    <unifiedKolDialog 
      v-model:dialogVisible="addManualKolDialogVisible"
      :dialogType="'add'"
      :mediumOptions="medium"
      :mcnList="mcnList.list || []" 
      :platformType="taskDetail?.task?.platform_type || 1"
      :orderMethod="taskDetail?.task?.order_method || 1"
      :orderType="taskDetail?.task?.order_type"
      :taskType="taskDetail?.task?.task_type"
      @cancel="cancelAddManualKol"
      @confirm="confirmAddManualKol"
    ></unifiedKolDialog>
    <el-dialog title="导入达人" :close-on-click-modal="false" :append-to-body="true" v-model="dialogTaskVisible">
      <el-form :rules="rules">
        <el-form-item label="上传文件" prop="upload_path">
          <div>
            <a
              :href="excel_url"
              class="purple"
              target="_blank"
              >模板下载</a
            >
            <el-upload
              style="width: 300px"
              :limit="1"
              ref="uploadRef"
              :auto-upload="false"
              :on-change="onSuccessLoad"
              accept=".xlsx"
              :file-list="fileList"
              :on-exceed="onExceed"
              drag
              action="#"
            >
              <el-icon style="font-size: 60px; color: #b8bcc5">
                <UploadFilled />
              </el-icon>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="()=>{dialogTaskVisible = false}">取 消</el-button>
        <el-button type="primary" @click="dialogTaskVisible = false">确 定</el-button>
      </template>
    </el-dialog>
    <!-- 进度条遮罩层 -->
    <ProgressOverlay :show="progressLevel" :percentage="percentage" text="文件解析中..." />
    <el-dialog v-model="dialogTaskVisible1" width="50%">
      <el-form :model="addTaskForm" :rules="rule" ref="ruleForm" label-width="auto" style="max-width: 700px" class="form-style">
        <span class="form-span">基本信息</span>
        <el-form-item label="所属项目" prop="project_id">
          <el-select
            style="width: 200px"
            v-model="addTaskForm.project_id"
            filterable
            remote
            reserve-keyword
            placeholder="请选择项目名称"
            :remote-method="remoteMethod1"
            :loading="loading"
          >
            <el-option v-for="item in projectList" :key="item.id" :label="item.project_name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="*任务名称" prop="task_name">
          <el-input v-model="addTaskForm.task_name" show-word-limit maxlength="50" />
        </el-form-item>
        <span class="form-span">任务类型</span>
        <p class="mt-8 mb-2" style="font-size: 14px; color: #333">*推广平台和体裁</p>
        <div
          class="flex"
          style="align-items: center; padding: 20px 10px; border: 1px solid rgb(170, 63, 200); border-radius: 5px; width: 300px"
        >
          <div>
            <p style="color: rgb(170, 63, 200); font-size: 14px; margin-bottom: 5px">抖音</p>
            <p style="color: #999; font-size: 12px">王牌渠道锁定新生代消费主力</p>
          </div>
        </div>
        <p class="mt-8 mb-2" style="font-size: 14px; color: #333">*任务类型</p>
        <div
          class="flex mb-5"
          style="align-items: center; padding: 20px 10px; border: 1px solid rgb(170, 63, 200); border-radius: 5px; width: 300px"
        >
          <div>
            <p style="color: rgb(170, 63, 200); font-size: 14px; margin-bottom: 5px">指派</p>
            <p style="color: #999; font-size: 12px">有明确达人或想指定达人接单</p>
          </div>
        </div>
        <span class="form-span">结算方式</span>
        <p class="mt-8 mb-2" style="font-size: 14px; color: #333">*结算方式</p>
        <div
          class="flex"
          style="align-items: center; padding: 20px 10px; border: 1px solid rgb(170, 63, 200); border-radius: 5px; width: 300px"
        >
          <div>
            <p style="color: rgb(170, 63, 200); font-size: 14px; margin-bottom: 5px">按一口价结算</p>
            <p style="color: #999; font-size: 12px">以固定价格与达人合作</p>
          </div>
        </div>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="createTaskConfirm(ruleForm)">创建任务</el-button>
        <el-button @click="closeDrawer">取消</el-button>
      </template>
    </el-dialog>
    <el-dialog
      title="批量设置建联媒介"
      width="400px"
      :close-on-click-modal="false"
      :append-to-body="true"
      v-model="dialogInformation"
    >
      <el-form>
        <el-form-item label="选择媒介" prop="kol_id">
          <el-select style="width: 150px" v-model="alliance_personnel" remote filterable placeholder="搜索建联媒介">
            <el-option v-for="i in medium" :key="i.value" :value="i.id" :label="i.name"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="setKol">确定</el-button>
        <el-button @click="dialogInformation = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 历史返点对话框 -->
    <el-dialog
      title="历史返点信息"
      :close-on-click-modal="false"
      :append-to-body="true"
      v-model="historyPointDialogVisible"
      @close="closeHistoryPointDialog"
    >
      <div v-loading="loading4" class="history-point-dialog">
        <div v-if="currentHistoryKol" class="kol-info-header">
          <div class="kol-avatar">
            <img :src="currentHistoryKol.kol_photo || userFaceImg" alt="达人头像" />
          </div>
          <div class="kol-details">
            <h3>{{ currentHistoryKol.kol_name }}</h3>
            <p>达人ID: {{ currentHistoryKol.platform_uid }}</p>
          </div>
        </div>

        <div class="history-table-container">
          <el-table
            :data="history_point_lists"
            border
            stripe
            :header-cell-style="{ textAlign: 'center', backgroundColor: '#f5f7fa', color: '#606266' }"
            style="width: 100%"
          >
            <el-table-column property="demand_name" label="视频信息" >
              <template #default="{ row }">
                <div class="video-info">
                  <el-tooltip :content="row?.title" placement="bottom" effect="light" :disabled="!row?.title">
                    <a :href="row?.video_url" target="_blank" class="video-link">
                      {{ row?.title || '无标题' }}
                    </a>
                  </el-tooltip>
                  <div class="video-time">{{ row?.release_time || '无发布时间' }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="任务信息" width="220">
              <template #default="{ row }">
                <div class="task-info">
                  <div class="task-name">
                    <el-tooltip :content="row?.demand_name" placement="bottom" effect="light" :disabled="!row?.demand_name">
                      <span>{{ row?.demand_name || '无任务名称' }}</span>
                    </el-tooltip>
                    <el-tag size="small" class="task-type-tag">{{ demandType[row?.demand_type] || '未知' }}</el-tag>
                  </div>
                  <div class="task-id">任务ID: {{ row?.demand_id || '无' }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="客户信息" width="180" align="center">
              <template #default="{ row }">
                <div class="customer-info">
                  <div class="account-name">{{ row?.account_name || '无账户名称' }}</div>
                  <div class="star-id">账户: {{ row?.star_id || '无' }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column property="rebate_ratio" label="返点信息" align="center" width="120">
              <template #default="{ row }">
                <span class="rebate-ratio">{{ row?.rebate_ratio || 0 }}%</span>
              </template>
            </el-table-column>

            <el-table-column property="media_name" label="媒介人员" align="center" width="120">
              <template #default="{ row }">
                {{ row?.media_name || '无' }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <el-button @click="closeHistoryPointDialog">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import addKolDialog from "../components/addKolDialog.vue";
import submitReport from "../components/submitReport.vue";
import unifiedKolDialog from "../components/unifiedKolDialog.vue"; // 导入统一的达人组件
import userFaceImg from "@/assets/img/default-face.png";

import {
  addTaskDataStatus,
  taskProcessKolDetail,
  contractInformationList,
  mcnListApi,
  historyReturnPointApi,
  nonStarKolApi,
  uploadKolApi,
  uploadKolApiNew,
  getRoleUsersThirdApi,
  getRoleUsersFirstApi,
  getRoleUsersSecondApi,
  getRoleUsersInformationApi,
  searchProjects,
  addTasks,
  mprojectsTaskApi,
  postRealTimePrice,
  exportKolListApi
} from "@/api/modules/business";
import { uploadMcnAnnex } from "@/api/modules/kol";
import { useTabsStore } from "@/stores/modules/tabs";
import { useRoute, useRouter } from "vue-router";
import { useSocketStore } from "@/stores/socketIo/socketStore.js"; // 引入 Pinia store
import { ref, onMounted, nextTick, watch, h, onBeforeUnmount, computed } from "vue";
import { ElMessage, ElNotification, ElMessageBox, ElCheckbox, ElSelect, ElOption, ElInput, ElButton, ElTooltip, ElPopconfirm, TableV2FixedDir, ElAutoResizer, ElTableV2 } from "element-plus";
import ProgressOverlay from "@/components/ProgressOverlay/index.vue";
import { Warning, Check, Close, Plus } from '@element-plus/icons-vue';

const tabStore = useTabsStore();
const socketStore = useSocketStore();
const totalGrossMargin = ref(0);
const route = useRoute();
const router = useRouter();
const kol_id = ref("");
const alliance_personnel = ref("");
const kolListRef = ref(null);
const loading = ref(false);
const dialogVisible = ref(false);
const addKolDialogVisible = ref(false);
const taskDetail = ref("");
const intervalId = ref();
const dialogType = ref("add");
const addSubmitReportVisible = ref(false);
const mcnNameList = ref([]);
const mcnList = ref({});
const selectionKolList = ref([]);
const dialogInformation = ref(false);
const customerUscList = ref([]);
const processInfo = ref({});
const roleUsers1 = ref([]);
const roleUsers2 = ref([]);
const roleUsers3 = ref([]);
const rowItem = ref({});
const page_type = ref("");
const active = ref(0);
const dialogTaskVisible = ref(false);
const dialogTaskVisible1 = ref(false);
const contract_detail = ref({});
const fileList = ref([]);

// 页面加载状态
const pageLoading = ref(true);

const isNeedReview = ref(false);
const isCurrentObj = ref();
const kolSelectConfirm = ref([]);
const contract_id = ref("");
const currentPerson = ref([]);
const contractPage = ref(1);
const total = ref(0);
const collectionType = {
  1: "完成",
  2: "采集中",
  3: "采集失败"
};
const platform = {
  1: '抖音',
  2: '抖音星立方',
  3: '小红书',
  4: '快手',
  5: 'B站',
  6: '腾讯互选'
};
//下单方式
const orderMethod = {
  1: '星推下单',
  2: '线下下单'
};

// 抖音任务模式映射
const taskModeDouyin = {
  1: '指派',
  2: '招募',
  3: '推广'
};

// B站任务模式映射
const taskModeBilibili = {
  1: '京火',
  2: '花火'
};

const kolList = ref([]);

// 选中的行数据
const selectedRows = ref([]);

// 搜索相关
const talentSearchKeyword = ref('');
const filteredKolList = ref([]);

// 招募任务信息（只读显示）
const recruitmentInfo = ref({
  // 结算方式
  settlementType: '',
  paymentMethod: '',
  settlementCycle: '',
  settlementStatus: '',

  // 任务要求
  contentRequirements: '',
  publishTimeRequirements: '',
  talentRequirements: '',
  fansRequirements: '',
  regionRequirements: '',
  industryRequirements: '',
  otherRequirements: '',
  prohibitedRequirements: '',

  // 任务信息
  recruitmentStartTime: '',
  recruitmentEndTime: '',
  expectedRecruitmentCount: '',
  currentRecruitmentCount: '',
  recruitmentStatus: '',
  taskPriority: ''
});

// 文件列表响应式变量
const referenceMaterialFileList = ref([]);
const taskIconFileList = ref([]);

// 招募表单数据（可编辑）
const recruitmentForm = ref({
  // 总利润信息表单
  assess_total_budget: 0, // 任务预估总预算 - float
  expect_coop_num: 0,  // 期望合作达人数 - int
  assess_total_gross: 0, // 预估总毛利 - float
  assess_total_gross_rate: 0, // 预估总毛利率 - float

  // 结算方式表单
  settlement_method: 0, // 结算方式 - int (1:按一口价结算, 2:按自然播放量结算)
  recruit_type: '', // 招募形式 - str
  selected_kol_type: '', // 选择达人团 - str
  bottom_mode: '', // 保底模式 - str
  assess_indicator: '', // 考核指标 - str
  kol_bottom_min_price: 0, // 达人保底价最低 - float
  kol_bottom_max_price: 0, // 达人保底价最高 - float
  kol_min_price: 0, // 达人一口价最低 - float
  kol_max_price: 0, // 达人一口价最高 - float
  cpm_price: 0, // CPM单价 - float
  dou_is_cal_price: '', // DOU+流量是否计费 - str
  service_select_mode: '', // 服务商选择方式 - str
  service_rebate_rate: 0, // 服务商返点比例 - float
  enroll_date: null, // 报名日期 - date array

  // 任务要求表单
  lens_requirement: '', // 镜头要求 - str
  reference_material: [], // 参考素材 - array
  task_enroll_date: null, // 任务报名日期 - date array
  expect_save_time: 0, // 期望保留时长 - int

  // 任务信息表单
  taskName: '', // 任务名称 - str
  kol_task_name: '', // 达人侧任务名称 - str
  task_icon: [], // 任务图标 - array
  xingtu_activity_ip: '', // 星图活动IP - str
  product_name: '', // 产品名称 - str
  product_desc: '', // 产品介绍 - str
  product_link: '' // 商品链接 - str
});

// 组件信息表单数据（仅在抖音平台招募任务模式下使用）
const componentForm = ref({
  is_publish_adv: [], // 选中的广告类型 - array
  effect_adv_id: '', // 巨量广告主ID-效果广告 - str
  brand_adv_id: '', // 巨量广告主ID-品牌广告 - str
  qianchuan_adv_id: '', // 巨量千川广告主ID - str
  dou_adv_id: '', // Dou+广告主ID - str
  dou_adv_uid: '', // Dou+广告主抖音UID - str
  component_type: 0, // 组件名称 - int
  component_content: '', // 组件文案 - str
  component_url: '', // 组件链接 - str
  component_remark: '', // 组件备注 - str
  extra_remark: '' // 其他特殊备注 - str
});

// 表单引用
const profitFormRef = ref(null);
const settlementFormRef = ref(null);
const taskRequirementsFormRef = ref(null);
const taskInfoFormRef = ref(null);
const componentFormRef = ref(null);

// 组件信息表单验证规则
const componentRules = ref({
  effect_adv_id: [
    {
      validator: (rule, value, callback) => {
        if (componentForm.value.is_publish_adv.includes('1') && !value) {
          callback(new Error('请输入巨量广告主ID-效果广告'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  brand_adv_id: [
    {
      validator: (rule, value, callback) => {
        if (componentForm.value.is_publish_adv.includes('2') && !value) {
          callback(new Error('请输入巨量广告主ID-品牌广告'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  qianchuan_adv_id: [
    {
      validator: (rule, value, callback) => {
        if (componentForm.value.is_publish_adv.includes('3') && !value) {
          callback(new Error('请输入巨量千川广告主ID'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  dou_adv_id: [
    {
      validator: (rule, value, callback) => {
        if (componentForm.value.is_publish_adv.includes('4') && !value) {
          callback(new Error('请输入Dou+广告主ID'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  dou_adv_uid: [
    {
      validator: (rule, value, callback) => {
        if (componentForm.value.is_publish_adv.includes('4') && !value) {
          callback(new Error('请输入Dou+广告主抖音UID'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
});

// 表单验证规则
const recruitmentRules = ref({
  // 总利润信息验证规则
  assess_total_budget: [
    { required: true, message: '请输入任务预估总预算', trigger: 'blur' },
  ],
  expect_coop_num: [
    { required: true, message: '请输入期望合作达人数', trigger: 'blur' },
    { type: 'number', min: 1, message: '达人数必须大于0', trigger: 'blur' }
  ],
  assess_total_gross: [
    { required: true, message: '请输入预估总毛利', trigger: 'blur' },
  ],
  assess_total_gross_rate: [
    { required: true, message: '请输入预估总毛利率', trigger: 'blur' },
  ],

  // 结算方式验证规则
  settlement_method: [
    { required: true, message: '请选择结算方式', trigger: 'change' }
  ],
  recruit_type: [
    {
      validator: (_rule, value, callback) => {
        const disabled = settlementFieldsDisabled.value.recruit_type;
        if (!disabled && !value) {
          callback(new Error('请选择招募形式'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ],
  assess_indicator: [
    {
      validator: (_rule, value, callback) => {
        const disabled = settlementFieldsDisabled.value.assess_indicator;
        if (!disabled && !value) {
          callback(new Error('请选择考核指标'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ],

  // 其他结算方式字段的动态验证规则（这些字段不是必填，只需要确保被禁用时不触发验证）
  selected_kol_type: [],
  bottom_mode: [],
  kol_bottom_min_price: [
    {
      validator: (_rule, value, callback) => {
        const disabled = settlementFieldsDisabled.value.kol_bottom_min_price;
        // 达人保底价不是必填字段，只在未禁用且有值时进行格式验证
        if (!disabled && value !== null && value !== undefined && value < 0) {
          callback(new Error('达人保底价必须大于等于0'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  kol_min_price: [
    {
      validator: (_rule, value, callback) => {
        const disabled = settlementFieldsDisabled.value.kol_min_price;
        // 达人一口价不是必填字段，只在未禁用且有值时进行格式验证
        if (!disabled && value !== null && value !== undefined && value < 0) {
          callback(new Error('达人一口价必须大于等于0'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  dou_is_cal_price: [],
  service_select_mode: [],

  enroll_date: [
    { required: true, message: '请选择报名日期', trigger: 'change' }
  ],
  cpm_price: [
    {
      validator: (_rule, value, callback) => {
        const disabled = settlementFieldsDisabled.value.cpm_price;
        // CPM单价不是必填字段，只在未禁用且有值时进行格式验证
        if (!disabled && value !== null && value !== undefined && value < 0) {
          callback(new Error('CPM单价必须大于等于0'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  service_rebate_rate: [
    { type: 'number', min: 0, max: 100, message: '返点比例必须在0-100之间', trigger: 'blur' }
  ],

  // 任务要求验证规则
  lens_requirement: [
    { required: true, message: '请输入镜头要求', trigger: 'blur' },
    { max: 300, message: '镜头要求不能超过300字', trigger: 'blur' }
  ],
  reference_material: [
    { required: true, message: '请上传参考素材', trigger: 'change' }
  ],
  task_enroll_date: [
    { required: true, message: '请选择报名日期', trigger: 'change' }
  ],
  expect_save_time: [
    { required: true, message: '请输入期望保留时长', trigger: 'blur' },
    { type: 'number', min: 1, message: '保留时长必须大于0天', trigger: 'blur' }
  ],

  // 任务信息验证规则
  taskName: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { max: 50, message: '任务名称不能超过50字', trigger: 'blur' }
  ],
  kol_task_name: [
    { required: true, message: '请输入达人侧任务名称', trigger: 'blur' },
    { max: 50, message: '达人侧任务名称不能超过50字', trigger: 'blur' }
  ],
  task_icon: [
    { required: true, message: '请上传任务图标', trigger: 'change' }
  ],
  product_name: [
    { required: true, message: '请输入产品名称', trigger: 'blur' },
    { max: 40, message: '产品名称不能超过40字', trigger: 'blur' }
  ],
  product_desc: [
    { required: true, message: '请输入产品介绍', trigger: 'blur' },
    { max: 1000, message: '产品介绍不能超过1000字', trigger: 'blur' }
  ],
  product_link: [
    { type: 'url', message: '请输入正确的链接格式', trigger: 'blur' }
  ]
});

// 初始化招募信息
const initRecruitmentInfo = (taskData) => {
  // 从任务数据中提取招募相关信息
  const task = taskData.task || {};
  const project = taskData.project || {};

  // 尝试从task_info字段中获取招募任务信息
  let recruitmentTaskInfo = null;
  if (task.task_info) {
    try {
      // 如果task_info是字符串，则解析；如果已经是对象，则直接使用
      recruitmentTaskInfo = typeof task.task_info === 'string' ? JSON.parse(task.task_info) : task.task_info;
    } catch (error) {
    }
  }

  // 初始化总利润信息表单数据 - 优先使用task直接字段，兼容task_info
  recruitmentForm.value.assess_total_budget = task.assess_total_budget || recruitmentTaskInfo?.assess_total_budget || 0;
  recruitmentForm.value.expect_coop_num = task.expect_coop_num || recruitmentTaskInfo?.expect_coop_num || 0;
  recruitmentForm.value.assess_total_gross = task.assess_total_gross || recruitmentTaskInfo?.assess_total_gross || 0;
  recruitmentForm.value.assess_total_gross_rate = task.assess_total_gross_rate || recruitmentTaskInfo?.assess_total_gross_rate || 0;

  // 初始化结算方式表单数据 - 优先使用task直接字段，兼容task_info
  recruitmentForm.value.settlement_method = task.settlement_method || recruitmentTaskInfo?.settlement_method || 0;
  recruitmentForm.value.recruit_type = task.recruit_type || recruitmentTaskInfo?.recruit_type || '';
  recruitmentForm.value.selected_kol_type = task.selected_kol_type || recruitmentTaskInfo?.selected_kol_type || '';
  recruitmentForm.value.bottom_mode = task.bottom_mode || recruitmentTaskInfo?.bottom_mode || '';
  recruitmentForm.value.assess_indicator = task.assess_indicator || recruitmentTaskInfo?.assess_indicator || '';

  // 处理字符串类型的数字字段
  recruitmentForm.value.kol_bottom_min_price = parseFloat(task.kol_bottom_min_price || recruitmentTaskInfo?.kol_bottom_min_price || 0);
  recruitmentForm.value.kol_bottom_max_price = parseFloat(task.kol_bottom_max_price || recruitmentTaskInfo?.kol_bottom_max_price || 0);
  recruitmentForm.value.kol_min_price = parseFloat(task.kol_min_price || recruitmentTaskInfo?.kol_min_price || 0);
  recruitmentForm.value.kol_max_price = parseFloat(task.kol_max_price || recruitmentTaskInfo?.kol_max_price || 0);
  recruitmentForm.value.cpm_price = parseFloat(task.cpm_price || recruitmentTaskInfo?.cpm_price || 0);
  recruitmentForm.value.service_rebate_rate = parseFloat(task.service_rebate_rate || recruitmentTaskInfo?.service_rebate_rate || 0);

  recruitmentForm.value.dou_is_cal_price = task.dou_is_cal_price || recruitmentTaskInfo?.dou_is_cal_price || '';
  recruitmentForm.value.service_select_mode = task.service_select_mode || recruitmentTaskInfo?.service_select_mode || '';

  // 处理日期数组字段
  try {
    const enrollDate = task.enroll_date || recruitmentTaskInfo?.enroll_date;
    recruitmentForm.value.enroll_date = enrollDate ? (typeof enrollDate === 'string' ? JSON.parse(enrollDate) : enrollDate) : null;
  } catch (error) {
    recruitmentForm.value.enroll_date = null;
  }

  // 初始化任务要求表单数据 - 优先使用task直接字段，兼容task_info
  recruitmentForm.value.lens_requirement = task.lens_requirement || recruitmentTaskInfo?.lens_requirement || '';
  recruitmentForm.value.expect_save_time = task.expect_save_time || recruitmentTaskInfo?.expect_save_time || 0;

  // 处理参考素材字段 - 现在存储为URL字符串
  try {
    const referenceMaterial = task.reference_material || recruitmentTaskInfo?.reference_material;
    if (referenceMaterial) {
      if (typeof referenceMaterial === 'string') {
        // 尝试解析JSON，如果失败则当作URL字符串处理
        try {
          const parsed = JSON.parse(referenceMaterial);
          if (Array.isArray(parsed)) {
            // 旧格式：对象数组，提取URL
            recruitmentForm.value.reference_material = parsed.map(item => item.url || item.name || '').filter(Boolean).join(',');
            referenceMaterialFileList.value = parsed.map((item, index) => ({
              name: item.name || `image_${index + 1}`,
              url: item.url || item.name || ''
            }));
          } else {
            // 新格式：URL字符串
            recruitmentForm.value.reference_material = referenceMaterial;
            const urls = referenceMaterial.split(',').filter(Boolean);
            referenceMaterialFileList.value = urls.map((url, index) => ({
              name: url.split('/').pop() || `image_${index + 1}`,
              url: url
            }));
          }
        } catch (parseError) {
          // 直接当作URL字符串处理
          recruitmentForm.value.reference_material = referenceMaterial;
          const urls = referenceMaterial.split(',').filter(Boolean);
          referenceMaterialFileList.value = urls.map((url, index) => ({
            name: url.split('/').pop() || `image_${index + 1}`,
            url: url
          }));
        }
      } else {
        recruitmentForm.value.reference_material = '';
        referenceMaterialFileList.value = [];
      }
    } else {
      recruitmentForm.value.reference_material = '';
      referenceMaterialFileList.value = [];
    }
  } catch (error) {
    recruitmentForm.value.reference_material = '';
    referenceMaterialFileList.value = [];
  }

  // 处理任务报名日期数组字段
  try {
    const taskEnrollDate = task.task_enroll_date || recruitmentTaskInfo?.task_enroll_date;
    recruitmentForm.value.task_enroll_date = taskEnrollDate ? (typeof taskEnrollDate === 'string' ? JSON.parse(taskEnrollDate) : taskEnrollDate) : null;
  } catch (error) {
    recruitmentForm.value.task_enroll_date = null;
  }

  // 初始化任务信息表单数据 - 优先使用task直接字段，兼容task_info
  recruitmentForm.value.kol_task_name = task.kol_task_name || recruitmentTaskInfo?.kol_task_name || '';
  recruitmentForm.value.xingtu_activity_ip = task.xingtu_activity_ip || recruitmentTaskInfo?.xingtu_activity_ip || '';
  recruitmentForm.value.product_name = task.product_name || recruitmentTaskInfo?.product_name || '';
  recruitmentForm.value.product_desc = task.product_desc || recruitmentTaskInfo?.product_desc || '';
  recruitmentForm.value.product_link = task.product_link || recruitmentTaskInfo?.product_link || '';

  // 处理任务图标字段 - 现在存储为URL字符串
  try {
    const taskIcon = task.task_icon || recruitmentTaskInfo?.task_icon;
    if (taskIcon) {
      if (typeof taskIcon === 'string') {
        // 尝试解析JSON，如果失败则当作URL字符串处理
        try {
          const parsed = JSON.parse(taskIcon);
          if (Array.isArray(parsed) && parsed.length > 0) {
            // 旧格式：对象数组，提取第一个URL
            const firstItem = parsed[0];
            recruitmentForm.value.task_icon = firstItem.url || firstItem.name || '';
            taskIconFileList.value = [{
              name: firstItem.name || 'task_icon',
              url: firstItem.url || firstItem.name || ''
            }];
          } else {
            // 新格式：URL字符串
            recruitmentForm.value.task_icon = taskIcon;
            taskIconFileList.value = [{
              name: taskIcon.split('/').pop() || 'task_icon',
              url: taskIcon
            }];
          }
        } catch (parseError) {
          // 直接当作URL字符串处理
          recruitmentForm.value.task_icon = taskIcon;
          taskIconFileList.value = [{
            name: taskIcon.split('/').pop() || 'task_icon',
            url: taskIcon
          }];
        }
      } else {
        recruitmentForm.value.task_icon = '';
        taskIconFileList.value = [];
      }
    } else {
      recruitmentForm.value.task_icon = '';
      taskIconFileList.value = [];
    }
  } catch (error) {
    recruitmentForm.value.task_icon = '';
    taskIconFileList.value = [];
  }

  // 初始化组件信息表单数据（仅在抖音平台招募任务模式下） - 优先使用task直接字段，兼容task_info
  componentForm.value.effect_adv_id = task.effect_adv_id || recruitmentTaskInfo?.effect_adv_id || '';
  componentForm.value.brand_adv_id = task.brand_adv_id || recruitmentTaskInfo?.brand_adv_id || '';
  componentForm.value.qianchuan_adv_id = task.qianchuan_adv_id || recruitmentTaskInfo?.qianchuan_adv_id || '';
  componentForm.value.dou_adv_id = task.dou_adv_id || recruitmentTaskInfo?.dou_adv_id || '';
  componentForm.value.dou_adv_uid = task.dou_adv_uid || recruitmentTaskInfo?.dou_adv_uid || '';
  componentForm.value.component_type = task.component_type || recruitmentTaskInfo?.component_type || 0;
  componentForm.value.component_content = task.component_content || recruitmentTaskInfo?.component_content || '';
  componentForm.value.component_url = task.component_url || recruitmentTaskInfo?.component_url || '';
  componentForm.value.component_remark = task.component_remark || recruitmentTaskInfo?.component_remark || '';
  componentForm.value.extra_remark = task.extra_remark || recruitmentTaskInfo?.extra_remark || '';

  // 处理是否投放广告数组字段
  try {
    const isPublishAdv = task.is_publish_adv || recruitmentTaskInfo?.is_publish_adv;
    componentForm.value.is_publish_adv = isPublishAdv ? (typeof isPublishAdv === 'string' ? JSON.parse(isPublishAdv) : isPublishAdv) : [];
  } catch (error) {
    componentForm.value.is_publish_adv = [];
  }

  // 结算方式
  recruitmentInfo.value.settlementType = task.settlement_type || '按一口价结算';
  recruitmentInfo.value.paymentMethod = task.payment_method || '月结';
  recruitmentInfo.value.settlementCycle = task.settlement_cycle || '30天';
  recruitmentInfo.value.settlementStatus = task.settlement_status || '未结算';

  // 任务要求
  recruitmentInfo.value.contentRequirements = task.content_requirements || '待填写';
  recruitmentInfo.value.publishTimeRequirements = task.publish_time_requirements || '待填写';
  recruitmentInfo.value.talentRequirements = task.talent_requirements || '待填写';
  recruitmentInfo.value.fansRequirements = task.fans_requirements || '待填写';
  recruitmentInfo.value.regionRequirements = task.region_requirements || '不限';
  recruitmentInfo.value.industryRequirements = task.industry_requirements || '不限';
  recruitmentInfo.value.otherRequirements = task.other_requirements || '无';
  recruitmentInfo.value.prohibitedRequirements = task.prohibited_requirements || '无';

  // 任务信息
  recruitmentInfo.value.recruitmentStartTime = task.recruitment_start_time || task.created_at || '待设置';
  recruitmentInfo.value.recruitmentEndTime = task.recruitment_end_time || '待设置';
  recruitmentInfo.value.expectedRecruitmentCount = task.expected_recruitment_count || '待设置';
  recruitmentInfo.value.currentRecruitmentCount = taskData.kols?.length || 0;
  recruitmentInfo.value.recruitmentStatus = task.recruitment_status || '招募中';
  recruitmentInfo.value.taskPriority = task.task_priority || '普通';
};

// 计算利润信息 - 当预算或达人数变化时
const calculateProfitInfo = () => {
  // 这里可以添加自动计算逻辑
  // 例如：根据预算和达人数计算平均成本等
};

// 计算毛利率 - 当毛利金额变化时
const calculateProfitMargin = () => {
  if (recruitmentForm.value.assess_total_budget && recruitmentForm.value.assess_total_gross) {
    const margin = (recruitmentForm.value.assess_total_gross / recruitmentForm.value.assess_total_budget * 100).toFixed(2);
    recruitmentForm.value.assess_total_gross_rate = parseFloat(margin);
  }
};

// 计算毛利金额 - 当毛利率变化时
const calculateProfitAmount = () => {
  if (recruitmentForm.value.assess_total_budget && recruitmentForm.value.assess_total_gross_rate) {
    const profit = (recruitmentForm.value.assess_total_budget * recruitmentForm.value.assess_total_gross_rate / 100).toFixed(2);
    recruitmentForm.value.assess_total_gross = parseFloat(profit);
  }
};

// 结算方式表单字段联动控制
const settlementFieldsDisabled = computed(() => {
  const isFixedPrice = recruitmentForm.value.settlement_method === 1; // 按一口价结算
  const isNaturalViews = recruitmentForm.value.settlement_method === 3; // 按自然播放量结算
  const isSelfService = recruitmentForm.value.recruit_type === '自助招募';
  const isNoGuarantee = recruitmentForm.value.bottom_mode === '无保底';
  const isInfoPage = page_type.value === 'info';
  const isChangePage = page_type.value === 'change';

  return {
    // 1. 招募形式：结算方式为"按自然播放量结算"时禁用
    recruit_type: isInfoPage || isChangePage || isNaturalViews,

    // 2. 选择达人团：结算方式为"按一口价结算"并且招募形式为"自助招募"时禁用，结算方式为"按自然播放量结算"时禁用
    selected_kol_type: isInfoPage || isChangePage || (isFixedPrice && isSelfService) || isNaturalViews,

    // 3. 保底模式：结算方式为"按一口价结算"时禁用
    bottom_mode: isInfoPage || isChangePage || isFixedPrice,

    // 4. 考核指标：结算方式为"按一口价结算"时禁用
    assess_indicator: isInfoPage || isChangePage || isFixedPrice,

    // 5. 达人保底价：结算方式为"按一口价结算"时禁用，结算方式为"按自然播放量结算"并且保底模式为"无保底"时禁用
    kol_bottom_min_price: isInfoPage || isChangePage || isFixedPrice || (isNaturalViews && isNoGuarantee),

    // 6. 达人一口价：结算方式为"按自然播放量结算"时禁用
    kol_min_price: isInfoPage || isChangePage || isNaturalViews,

    // 7. CPM单价：结算方式为"按一口价结算"时禁用
    cpm_price: isInfoPage || isChangePage || isFixedPrice,

    // 8. DOU+流量是否计费：结算方式为"按一口价结算"时禁用
    dou_is_cal_price: isInfoPage || isChangePage || isFixedPrice,

    // 9. 服务商选择方式：结算方式为"按一口价结算"并且招募形式为"自助招募"时禁用
    service_select_mode: isInfoPage || isChangePage || (isFixedPrice && isSelfService)
  };
});

// 监听结算方式变化，清空相关字段
watch(() => recruitmentForm.value.settlement_method, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    // 当结算方式变为"按自然播放量结算"时，清空招募形式
    if (newValue === 3) { // 按自然播放量结算
      recruitmentForm.value.recruit_type = '';
      recruitmentForm.value.selected_kol_type = '';
      recruitmentForm.value.kol_min_price = 0;
      recruitmentForm.value.kol_max_price = 0;
    }

    // 当结算方式变为"按一口价结算"时，清空相关字段
    if (newValue === 1) { // 按一口价结算
      recruitmentForm.value.bottom_mode = '';
      recruitmentForm.value.assess_indicator = '';
      recruitmentForm.value.kol_bottom_min_price = 0;
      recruitmentForm.value.kol_bottom_max_price = 0;
      recruitmentForm.value.cpm_price = 0;
      recruitmentForm.value.dou_is_cal_price = '';
    }
  }
});

// 监听招募形式变化，清空相关字段
watch(() => recruitmentForm.value.recruit_type, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    // 当招募形式变为"自助招募"且结算方式为"按一口价结算"时，清空选择达人团和服务商选择方式
    if (newValue === '自助招募' && recruitmentForm.value.settlement_method === 1) {
      recruitmentForm.value.selected_kol_type = '';
      recruitmentForm.value.service_select_mode = '';
    }
  }
});

// 监听保底模式变化，清空相关字段
watch(() => recruitmentForm.value.bottom_mode, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    // 当保底模式变为"无保底"且结算方式为"按自然播放量结算"时，清空达人保底价
    if (newValue === '无保底' && recruitmentForm.value.settlement_method === 3) {
      recruitmentForm.value.kol_bottom_min_price = 0;
      recruitmentForm.value.kol_bottom_max_price = 0;
    }
  }
});

// 图片上传相关处理函数
const handlePreview = (file) => {
  // 预览图片
  const img = new Image();
  img.src = file.url || file.raw;
  const imgWindow = window.open('');
  imgWindow.document.write(`<img src="${img.src}" style="max-width: 100%; max-height: 100%;" />`);
};





// 参考素材上传处理
const uploadReferenceMaterialRequest = (files) => {
  let formData = new FormData();
  formData.append("file", files.file);

  uploadMcnAnnex(formData).then(res => {
    if (recruitmentForm.value.reference_material && recruitmentForm.value.reference_material.length > 0) {
      recruitmentForm.value.reference_material += "," + res.data;
    } else {
      recruitmentForm.value.reference_material = res.data;
    }
    ElMessage.success('参考素材上传成功');
  }).catch(error => {
    console.error('参考素材上传失败:', error);
    ElMessage.error('参考素材上传失败');
  });
};

// 任务图标上传处理
const uploadTaskIconRequest = (files) => {
  let formData = new FormData();
  formData.append("file", files.file);

  uploadMcnAnnex(formData).then(res => {
    recruitmentForm.value.task_icon = res.data;
    ElMessage.success('任务图标上传成功');
  }).catch(error => {
    console.error('任务图标上传失败:', error);
    ElMessage.error('任务图标上传失败');
  });
};

// 参考素材删除处理
const handleReferenceMaterialRemove = (file, fileList) => {
  // 从URL字符串中移除对应的URL
  if (recruitmentForm.value.reference_material) {
    const urls = recruitmentForm.value.reference_material.split(',');
    const filteredUrls = urls.filter(url => url !== file.url);
    recruitmentForm.value.reference_material = filteredUrls.join(',');
  }
  referenceMaterialFileList.value = fileList;
};

// 任务图标删除处理
const handleTaskIconRemove = (file, fileList) => {
  recruitmentForm.value.task_icon = '';
  taskIconFileList.value = fileList;
};

// 计算表格总宽度
const totalTableWidth = computed(() => {
  return tableColumns.value.reduce((total, column) => {
    return total + (column.width || 120); // 默认列宽120px
  }, 0);
});

// 表格列配置
const tableColumns = computed(() => {
  // 检查是否为抖音招募任务
  const isDouyinRecruitmentTask = (taskDetail.value?.task?.platform_type === 1 || taskDetail.value?.task?.platform_type === 2) && taskDetail.value?.task?.task_type === 2;

  // 如果是抖音招募任务，使用特定的列配置
  if (isDouyinRecruitmentTask) {
    return [
      // 选择列
      {
        key: 'selection',
        width: 55,
        fixed: TableV2FixedDir.LEFT,
        cellRenderer: ({ rowData, rowIndex }) => {
          const isSelectable = isRowSelectable(rowData, rowIndex);
          const onChange = (value) => {
            if (value) {
              if (!selectedRows.value.includes(rowData)) {
                selectedRows.value.push(rowData);
              }
            } else {
              const index = selectedRows.value.findIndex(
                (item) =>
                  item.platform_uid === rowData.platform_uid &&
                  item.cooperation_type === rowData.cooperation_type
              );
              if (index > -1) {
                selectedRows.value.splice(index, 1);
              }
            }
            handleSelectionChange(selectedRows.value);
          };

          const isSelected = selectedRows.value.some(
            (item) =>
              item.platform_uid === rowData.platform_uid &&
              item.cooperation_type === rowData.cooperation_type
          );

          return h(
            "div",
            {
              style: {
                width: "100%",
                height: "80px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                boxSizing: "border-box",
              },
            },
            h(ElCheckbox, {
              modelValue: isSelected,
              disabled: !isSelectable,
              onChange,
            })
          );
        },
        headerCellRenderer: () => {
          const allSelectable = selectionKolList.value.filter(row => isRowSelectable(row));
          const allSelected = allSelectable.length > 0 && allSelectable.every(row =>
            selectedRows.value.some(item =>
              item.platform_uid === row.platform_uid &&
              item.cooperation_type === row.cooperation_type
            )
          );
          const indeterminate = selectedRows.value.length > 0 && !allSelected;

          const onChange = (value) => {
            if (value) {
              selectedRows.value = [...allSelectable];
            } else {
              selectedRows.value = [];
            }
            handleSelectionChange(selectedRows.value);
          };

          return h('div', {
            style: {
              width: '100%',
              height: '50px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxSizing: 'border-box'
            }
          },[
            h(ElCheckbox, {
              modelValue: allSelected,
              indeterminate,
              onChange
            })
          ]);
        }
      },
      // 达人头像列（包含达人名称和星图ID）
      {
        key: 'kol_name',
        title: '达人信息',
        dataKey: 'kol_name',
        width: 200,
        fixed: TableV2FixedDir.LEFT,
        cellRenderer: ({ rowData, rowIndex }) => {
          const isWarning = tableRowClassName({ rowData, rowIndex }) === "warning-row";
          const warningNode = isWarning
            ? h(
                "span",
                {
                  style: {
                    marginLeft: "8px",
                    color: "#e6a23c",
                    flexShrink: "0",
                  },
                  title:
                    "该达人毛利率低于15%或预估应收媒体返点比例（本次）低于达人历史最高返点比例，需要审核！",
                },
                "⚠️"
              )
            : null;

          return h(
            "div",
            {
              class: "kol-name-cell",
              style: {
                width: "100%",
                height: "80px",
                padding: "8px",
                display: "flex",
                alignItems: "center",
                boxSizing: "border-box",
              },
            },
            [
              h("img", {
                src: rowData?.kol_photo || userFaceImg,
                width: 45,
                height: 45,
                alt: "",
                style: {
                  borderRadius: "4px",
                  objectFit: "cover",
                  flexShrink: "0",
                  marginRight: "12px",
                },
              }),
              h(
                "div",
                {
                  style: {
                    flex: "1",
                    minWidth: "0",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                  },
                },
                [
                  h(
                    "div",
                    {
                      style: {
                        fontWeight: "600",
                        fontSize: "14px",
                        display: "flex",
                        alignItems: "center",
                        marginBottom: "4px",
                      },
                    },
                    [
                      h(
                        ElTooltip,
                        {
                          content: rowData?.kol_name || "",
                          placement: "top",
                          disabled: !rowData?.kol_name || rowData.kol_name.length <= 10,
                        },
                        {
                          default: () => h(
                            "span",
                            {
                              style: {
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                                maxWidth: "120px",
                              },
                            },
                            rowData?.kol_name || ""
                          )
                        }
                      ),
                      warningNode,
                    ].filter(Boolean)
                  ),
                  h(
                    "div",
                    {
                      style: {
                        fontSize: "12px",
                        color: "#6b7280",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                      },
                    },
                    [
                      h(
                        ElTooltip,
                        {
                          content: `ID:${rowData?.platform_uid || ""}`,
                          placement: "top",
                          disabled: !rowData?.platform_uid || `ID:${rowData.platform_uid}`.length <= 15,
                        },
                        {
                          default: () => h(
                            "span",
                            {
                              style: {
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                              },
                            },
                            `ID:${rowData?.platform_uid || ""}`
                          )
                        }
                      )
                    ]
                  )
                ]
              ),
            ]
          );
        },
      },
      // 达人裸价列
      {
        key: 'kol_price',
        title: '达人裸价',
        dataKey: 'kol_price',
        width: 150,
        cellRenderer: ({ rowData }) => {
          // 优先使用 process_info.kol_base_price，如果没有则使用 kol_price
          const kolPrice = rowData?.process_info?.kol_base_price || rowData?.kol_price || 0;
          return h('div', {
            style: {
              width: '100%',
              height: '80px',
              padding: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxSizing: 'border-box',
              fontSize: '14px'
            }
          }, `¥${kolPrice}`);
        }
      },
      // 粉丝量列
      {
        key: 'kol_fans_num',
        title: '粉丝量',
        dataKey: 'kol_fans_num',
        width: 150,
        cellRenderer: ({ rowData }) => {
          // 尝试从多个数据源获取粉丝量
          const fansNum = rowData?.kol_fans_num || rowData?.ext?.kol_fans_num || '';

          return h('div', {
            style: {
              width: '100%',
              height: '80px',
              padding: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxSizing: 'border-box',
              fontSize: '14px'
            }
          }, fansNum || '');
        }
      },
      // 星晓达人列
      {
        key: 'is_xingxiao_kol',
        title: '星晓达人',
        dataKey: 'is_xingxiao_kol',
        width: 120,
        cellRenderer: ({ rowData }) => {
          // 尝试从多个数据源获取星晓达人信息
          const isXingxiaoKol = rowData?.is_xingxiao_kol || rowData?.ext?.is_xingxiao_kol || '';

          return h('div', {
            style: {
              width: '100%',
              height: '80px',
              padding: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxSizing: 'border-box',
              fontSize: '14px'
            }
          }, isXingxiaoKol || '');
        }
      },
      // 建联媒介列
      {
        key: 'alliance_personnel',
        title: '建联媒介',
        dataKey: 'alliance_personnel',
        width: 200,
        cellRenderer: ({ rowData }) => {
          return h('div', {
            style: 'padding: 8px; height: 100%; display: flex; align-items: center; justify-content: center;'
          }, [
            page_type.value === 'info' ?
              h('span', { class: 'text-sm' }, rowData.alliance_personnel || '未设置') :
              h(ElSelect, {
                style: 'width: 160px',
                modelValue: rowData.alliance_personnel,
                remote: true,
                placeholder: '搜索建联媒介',
                filterable: true,
                onChange: (value) => {
                  rowData.alliance_personnel = value;
                  setMedium(rowData);
                }
              }, {
                default: () => medium.value.map(item =>
                  h(ElOption, {
                    key: item.value,
                    value: item.name,
                    label: item.name
                  })
                )
              })
          ]);
        }
      },
      // MCN机构列
      {
        key: 'mcn_short_name',
        title: 'MCN机构',
        dataKey: 'mcn_short_name',
        width: 220,
        cellRenderer: ({ rowData }) => {
          if (page_type.value === 'info') {
            return h('span', rowData.mcnObject?.mcn_short_name || '未设置');
          }

          return h(ElTooltip, {
            class: 'box-item',
            effect: 'dark',
            content: rowData.mcnObject?.mcn_short_name,
            placement: 'top',
            disabled: !rowData.mcnObject?.mcn_short_name
          }, {
            default: () => h(ElSelect, {
              style: 'width: 140px',
              valueKey: 'id',
              modelValue: rowData.mcnObject?.mcn_short_name,
              filterable: true,
              reserveKeyword: true,
              placeholder: '请选择MCN机构',
              onChange: (value) => {
                handleMcnChange(value, rowData);
              }
            }, {
              default: () => mcnList.value.list?.map(item =>
                h(ElOption, {
                  key: item.id,
                  label: item.mcn_short_name,
                  value: item
                })
              )
            })
          });
        }
      },
      // 联系方式列
      {
        key: 'contact_information',
        title: '联系方式',
        dataKey: 'contact_information',
        width: 160,
        cellRenderer: ({ rowData }) => {
          if (page_type.value === 'info') {
            return h('span', rowData.contact_information || '未设置');
          }

          return h(ElTooltip, {
            class: 'box-item',
            effect: 'dark',
            content: rowData.contact_information,
            placement: 'top',
            disabled: !rowData.contact_information
          }, {
            default: () => h(ElInput, {
              modelValue: rowData.contact_information,
              placeholder: '联系方式',
              style: 'width: 90%',
              onInput: (value) => {
                rowData.contact_information = value;
              }
            })
          });
        }
      },
      // 操作列
      {
        key: 'operations',
        title: '操作',
        dataKey: 'operations',
        width: 220,
        fixed: TableV2FixedDir.RIGHT,
        cellRenderer: ({ rowData }) => {
          const isInfoPage = page_type.value === 'info';

          // 提前计算"查看"按钮节点
          const viewButtonNode = isInfoPage
            ? h(
                ElButton,
                {
                  type: 'primary',
                  link: true,
                  onClick: () => openConnectInfo(rowData),
                  style: {
                    margin: '0',
                    padding: '4px 8px',
                  },
                },
                '查看'
              )
            : null;

          return h(
            'div',
            {
              class: 'operations-cell',
              style: {
                width: '100%',
                height: '80px',
                padding: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px',
                boxSizing: 'border-box',
              },
            },
            [
              h(
                ElButton,
                {
                  type: 'primary',
                  link: true,
                  disabled: isInfoPage,
                  onClick: () => handleConnectInfo(rowData),
                  style: {
                    margin: '0',
                    padding: '4px 8px',
                  },
                },
                () => '编辑'
              ),
              viewButtonNode,
              h(
                ElPopconfirm,
                {
                  title: '确定删除当前达人么?',
                  onConfirm: () => kolListDel(rowData.platform_uid, rowData.cooperation_type),
                },
                {
                  reference: () =>
                    h(
                      ElButton,
                      {
                        type: 'danger',
                        link: true,
                        disabled: isInfoPage,
                        style: {
                          margin: '0',
                          padding: '4px 8px',
                        },
                      },
                      () => '删除'
                    ),
                }
              ),
            ].filter(Boolean)
          );
        },
      }
    ];
  }

  // 非招募任务的原有列配置
  const columns = [
    // 选择列
    {
      key: 'selection',
      width: 55,
      fixed: TableV2FixedDir.LEFT,
      cellRenderer: ({ rowData, rowIndex }) => {
        const isSelectable = isRowSelectable(rowData, rowIndex);
        const onChange = (value) => {
          if (value) {
            if (!selectedRows.value.includes(rowData)) {
              selectedRows.value.push(rowData);
            }
          } else {
            const index = selectedRows.value.findIndex(
              (item) =>
                item.platform_uid === rowData.platform_uid &&
                item.cooperation_type === rowData.cooperation_type
            );
            if (index > -1) {
              selectedRows.value.splice(index, 1);
            }
          }
          handleSelectionChange(selectedRows.value);
        };

        const isSelected = selectedRows.value.some(
          (item) =>
            item.platform_uid === rowData.platform_uid &&
            item.cooperation_type === rowData.cooperation_type
        );

        return h(
          "div",
          {
            style: {
              width: "100%",
              height: "80px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              boxSizing: "border-box",
            },
          },
          h(ElCheckbox, {
            modelValue: isSelected,
            disabled: !isSelectable,
            onChange,
          })
        );
      },
      headerCellRenderer: () => {
        const allSelectable = selectionKolList.value.filter(row => isRowSelectable(row));
        const allSelected = allSelectable.length > 0 && allSelectable.every(row =>
          selectedRows.value.some(item =>
            item.platform_uid === row.platform_uid &&
            item.cooperation_type === row.cooperation_type
          )
        );
        const indeterminate = selectedRows.value.length > 0 && !allSelected;

        const onChange = (value) => {
          if (value) {
            selectedRows.value = [...allSelectable];
          } else {
            selectedRows.value = [];
          }
          handleSelectionChange(selectedRows.value);
        };

        return h('div', {
          style: {
            width: '100%',
            height: '50px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxSizing: 'border-box'
          }
        },[
          h(ElCheckbox, {
            modelValue: allSelected,
            indeterminate,
            onChange
          })
        ]);
      }
    },
    // 达人名称列
    {
      key: 'kol_name',
      title: '达人名称',
      dataKey: 'kol_name',
      width: 200,
      fixed: TableV2FixedDir.LEFT,
      cellRenderer: ({ rowData, rowIndex }) => {
        const isWarning = tableRowClassName({ rowData, rowIndex }) === "warning-row";
        const warningNode = isWarning
          ? h(
              "span",
              {
                style: {
                  marginLeft: "8px",
                  color: "#e6a23c",
                  flexShrink: "0",
                },
                title:
                  "该达人毛利率低于15%或预估应收媒体返点比例（本次）低于达人历史最高返点比例，需要审核！",
              },
              "⚠️"
            )
          : null;

        return h(
          "div",
          {
            class: "kol-name-cell",
            style: {
              width: "100%",
              height: "80px",
              padding: "8px",
              display: "flex",
              alignItems: "center",
              boxSizing: "border-box",
            },
          },
          [
            h("img", {
              src: rowData?.kol_photo || userFaceImg,
              width: 45,
              height: 45,
              alt: "",
              style: {
                borderRadius: "4px",
                objectFit: "cover",
                flexShrink: "0",
                marginRight: "12px",
              },
            }),
            h(
              "div",
              {
                style: {
                  flex: "1",
                  minWidth: "0",
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                },
              },
              [
                h(
                  "div",
                  {
                    style: {
                      fontWeight: "600",
                      fontSize: "14px",
                      display: "flex",
                      alignItems: "center",
                      marginBottom: "4px",
                    },
                  },
                  [
                    h(
                      "span",
                      {
                        style: {
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          maxWidth: "120px",
                        },
                      },
                      rowData?.kol_name || ""
                    ),
                    warningNode,
                  ].filter(Boolean)
                ),
                h(
                  "div",
                  {
                    style: {
                      fontSize: "12px",
                      color: "#6b7280",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                    },
                  },
                  `ID:${rowData?.platform_uid || ""}`
                ),
              ]
            ),
          ]
        );
      },
    }
  ];

  // 合作形式列 - 在抖音招募任务模式下隐藏
  if (!isDouyinRecruitmentTask) {
    columns.push({
      key: 'cooperation_type',
      title: '合作形式',
      dataKey: 'cooperation_type',
      width: 160,
      cellRenderer: ({ rowData }) => {
        const isInfoPage = page_type.value === "info";
        const selectNode = isInfoPage
          ? h(
              "span",
              {
                style: { fontSize: "14px" },
              },
              (() => {
                const option = placementListArr.value.find(
                  (item) => item.value === rowData.cooperation_type
                );
                return option?.label || "未知";
              })()
            )
          : h(
              ElSelect,
              {
                style: "width: 130px",
                modelValue: rowData.cooperation_type,
                onChange: (value) => {
                  rowData.cooperation_type = value;
                  changeType(rowData);
                },
              },
              {
                default: () =>
                  placementListArr.value.map((item) =>
                    h(ElOption, {
                      key: item.value,
                      value: item.value,
                      label: item.label,
                    })
                  ),
              }
            );

        return h(
          "div",
          {
            style: {
              width: "100%",
              height: "80px",
              padding: "8px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              boxSizing: "border-box",
            },
          },
          selectNode
        );
      },
    });
  }

  // 根据平台类型添加服务类型列（仅B站）
  if (taskDetail.value?.task?.platform_type === 5) {
    columns.push({
      key: 'bili_service_type',
      title: '服务类型',
      dataKey: 'bili_service_type',
      width: 180,
      cellRenderer: ({ rowData }) => {
        if (page_type.value === 'info') {
          if (rowData.process_info?.bili_service_type === 1) {
            return '个人UP主服务费 (任务金额*7%)';
          } else if (rowData.process_info?.bili_service_type === 2) {
            return '签约UP主服务费 (任务金额*5%)';
          } else {
            return '个人UP主服务费 (任务金额*7%)';
          }
        }

        return h(ElSelect, {
          style: 'width: 180px',
          modelValue: rowData.process_info?.bili_service_type,
          onChange: (value) => {
            if (!rowData.process_info) rowData.process_info = {};
            rowData.process_info.bili_service_type = value;
            updateBiliServiceType(rowData);
          }
        }, ()=> [
          h(ElOption, { value: 1, label: '个人UP主服务费 (任务金额*7%)' }),
          h(ElOption, { value: 2, label: '签约UP主服务费 (任务金额*5%)' })
        ]);
      }
    });
  }

  // 添加更多列
  columns.push(
    // 平台刊例价列
    {
      key: 'kol_price',
      title: '平台刊例价',
      dataKey: 'kol_price',
      width: 150,
      cellRenderer: ({ rowData }) => {
        return h('div', {
          style: {
            width: '100%',
            height: '80px',
            padding: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxSizing: 'border-box',
            fontSize: '14px'
          }
        }, `¥${rowData?.kol_price || 0}`);
      }
    },
    // 建联媒介列
    {
      key: 'alliance_personnel',
      title: '建联媒介',
      dataKey: 'alliance_personnel',
      width: 200,
      cellRenderer: ({ rowData }) => {
        return h('div', {
          style: 'padding: 8px; height: 100%; display: flex; align-items: center; justify-content: center;'
        }, [
          page_type.value === 'info' ?
            h('span', { class: 'text-sm' }, rowData.alliance_personnel || '未设置') :
            h(ElSelect, {
              style: 'width: 160px',
              modelValue: rowData.alliance_personnel,
              remote: true,
              placeholder: '搜索建联媒介',
              filterable: true,
              onChange: (value) => {
                rowData.alliance_personnel = value;
                setMedium(rowData);
              }
            }, {
              default: () => medium.value.map(item =>
                h(ElOption, {
                  key: item.value,
                  value: item.name,
                  label: item.name
                })
              )
            })
        ]);
      }
    }
  );

  // 历史返点列（仅抖音平台）
  if (taskDetail.value?.task?.platform_type === 1 || taskDetail.value?.task?.platform_type === 2) {
    columns.push({
      key: 'history_point',
      title: '历史返点',
      dataKey: 'history_point',
      width: 120,
      align: 'center',
      cellRenderer: ({ rowData }) => {
        return h('div', {
          style: {
            width: '100%',
            height: '80px',
            padding: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxSizing: 'border-box'
          }
        }, [
          h('span', {
            style: 'color: #008b7d;cursor: pointer;',
            onClick: () => {
              openHistoryPointDialog(rowData);
            }
          }, '查看')
        ]);
      }
    });
  }

  // 添加更多列
  columns.push(
    // MCN机构简称列
    {
      key: 'mcn_short_name',
      title: 'MCN机构简称',
      dataKey: 'mcn_short_name',
      width: 220,
      cellRenderer: ({ rowData }) => {
        if (page_type.value === 'info') {
          return h('span', rowData.mcnObject?.mcn_short_name || '未设置');
        }

        return h(ElTooltip, {
          class: 'box-item',
          effect: 'dark',
          content: rowData.mcnObject?.mcn_short_name,
          placement: 'top',
          disabled: !rowData.mcnObject?.mcn_short_name
        }, {
          default: () => h(ElSelect, {
            style: 'width: 140px',
            valueKey: 'id',
            modelValue: rowData.mcnObject?.mcn_short_name,
            filterable: true,
            reserveKeyword: true,
            placeholder: '请选择MCN机构',
            onChange: (value) => {
              handleMcnChange(value, rowData);
            }
          }, {
            default: () => mcnList.value.list?.map(item =>
              h(ElOption, {
                key: item.id,
                label: item.mcn_short_name,
                value: item
              })
            )
          })
        });
      }
    },
    // 联系方式列
    {
      key: 'contact_information',
      title: '联系方式',
      dataKey: 'contact_information',
      width: 160,
      cellRenderer: ({ rowData }) => {
        if (page_type.value === 'info') {
          return h('span', rowData.contact_information || '未设置');
        }

        return h(ElTooltip, {
          class: 'box-item',
          effect: 'dark',
          content: rowData.contact_information,
          placement: 'top',
          disabled: !rowData.contact_information
        }, {
          default: () => h(ElInput, {
            modelValue: rowData.contact_information,
            placeholder: '联系方式',
            onInput: (value) => {
              rowData.contact_information = value;
            }
          })
        });
      }
    }
  );

  // 采集状态列（仅抖音平台）
  if (taskDetail.value?.task?.platform_type === 1 || taskDetail.value?.task?.platform_type === 2) {
    columns.push({
      key: 'collection_status',
      title: '采集状态',
      dataKey: 'collection_status',
      width: 120,
      cellRenderer: ({ rowData }) => {
        return collectionType[rowData?.collection_status] || '';
      }
    });
  }

  // 操作列
  columns.push({
  key: 'operations',
  title: '操作',
  dataKey: 'operations',
  width: 220,
  fixed: TableV2FixedDir.RIGHT,
  cellRenderer: ({ rowData }) => {
    const isInfoPage = page_type.value === 'info';

    // 提前计算“查看”按钮节点
    const viewButtonNode = isInfoPage
      ? h(
          ElButton,
          {
            type: 'primary',
            link: true,
            onClick: () => openConnectInfo(rowData),
            style: {
              margin: '0',
              padding: '4px 8px',
            },
          },
          '查看'
        )
      : null;

    return h(
      'div',
      {
        class: 'operations-cell',
        style: {
          width: '100%',
          height: '80px',
          padding: '8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '8px',
          boxSizing: 'border-box',
        },
      },
      [
        h(
          ElButton,
          {
            type: 'primary',
            link: true,
            disabled: isInfoPage,
            onClick: () => handleConnectInfo(rowData),
            style: {
              margin: '0',
              padding: '4px 8px',
            },
          },
          () => '编辑'
        ),
        viewButtonNode,
        h(
          ElPopconfirm,
          {
            title: '确定删除当前达人么?',
            onConfirm: () => kolListDel(rowData.platform_uid, rowData.cooperation_type),
          },
          {
            reference: () =>
              h(
                ElButton,
                {
                  type: 'danger',
                  link: true,
                  disabled: isInfoPage,
                  style: {
                    margin: '0',
                    padding: '4px 8px',
                  },
                },
                () => '删除'
              ),
          }
        ),
      ].filter(Boolean)
    );
  },
});

  return columns;
});

//计算属性动态设置下载模版
const excel_url = computed(() => {
  switch (taskDetail.value?.task?.platform_type) {
    case 1:
      return taskDetail.value?.task?.order_method == 1 ? 'https://oss-yinlimedia-shanghai-common.oss-cn-shanghai.aliyuncs.com/media/kol_export_template/%E6%98%9F%E6%8E%A8%E4%B8%8B%E5%8D%95-%E6%8A%96%E9%9F%B3.xlsx' : 'https://oss-yinlimedia-shanghai-common.oss-cn-shanghai.aliyuncs.com/media/kol_export_template/%E9%9B%86%E9%87%87%E7%BA%BF%E4%B8%8B%E4%B8%8B%E5%8D%95-%E6%8A%96%E9%9F%B3.xlsx';
    case 2:
      return taskDetail.value?.task?.order_method == 1 ? 'https://oss-yinlimedia-shanghai-common.oss-cn-shanghai.aliyuncs.com/media/kol_export_template/%E6%98%9F%E6%8E%A8%E4%B8%8B%E5%8D%95-%E6%8A%96%E9%9F%B3.xlsx' : 'https://oss-yinlimedia-shanghai-common.oss-cn-shanghai.aliyuncs.com/media/kol_export_template/%E9%9B%86%E9%87%87%E7%BA%BF%E4%B8%8B%E4%B8%8B%E5%8D%95-%E6%8A%96%E9%9F%B3.xlsx';
    case 3:
      return 'https://oss-yinlimedia-shanghai-common.oss-cn-shanghai.aliyuncs.com/media/kol_export_template/%E9%9B%86%E9%87%87%E7%BA%BF%E4%B8%8B%E4%B8%8B%E5%8D%95-%E5%B0%8F%E7%BA%A2%E4%B9%A6.xlsx';
    case 4:
      return 'https://oss-yinlimedia-shanghai-common.oss-cn-shanghai.aliyuncs.com/media/kol_export_template/%E5%BF%AB%E6%89%8B%E6%A8%A1%E6%9D%BF.xlsx';
    case 5:
      return 'https://oss-yinlimedia-shanghai-common.oss-cn-shanghai.aliyuncs.com/media/kol_export_template/B%E7%AB%99%E6%A8%A1%E6%9D%BF_v2.xlsx';
    case 6:
      return 'https://oss-yinlimedia-shanghai-common.oss-cn-shanghai.aliyuncs.com/media/kol_export_template/%E9%9B%86%E9%87%87%E7%BA%BF%E4%B8%8B%E4%B8%8B%E5%8D%95-%E8%85%BE%E8%AE%AF%E4%BA%92%E9%80%89.xlsx';
    default:
      return 'https://oss-yinlimedia-shanghai-common.oss-cn-shanghai.aliyuncs.com/media/kol_export_template/%E6%98%9F%E6%8E%A8%E4%B8%8B%E5%8D%95-%E6%8A%96%E9%9F%B3.xlsx';
  }
});

// 将常量改为计算属性
const placementListArr = computed(() => {
  const platformType = taskDetail.value?.task?.platform_type || 1;
  
  // 根据平台类型返回不同的选项
  switch (platformType) {
    case 3: 
      return [
        { label: "图文笔记", value: 1 },
        { label: "视频笔记", value: 2 }
      ];
    case 4: 
      return [
        { label: "21-60S视频", value: 2 },
        { label: "60S以上视频", value: 3 }
      ];
    case 5: 
      // B站 - 根据任务类型提供不同的合作形式选项
      const taskType = taskDetail.value?.task?.task_type || 1;
      
      if (taskType === 2) { // 花火模式
        return [
          { label: "定制视频", value: 1 },
          { label: "植入视频", value: 2 },
          { label: "直发动态", value: 3 },
          { label: "转发动态", value: 4 },
          { label: "线下活动", value: 5 },
          { label: "参与拍摄", value: 6 },
          { label: "代投", value: 7 },
          { label: "创作授权", value: 8 },
          { label: "画师手绘", value: 9 },
          { label: "其他", value: 10 }
        ];
      }
      
      // 默认京火模式
      return [
        { label: "定制视频", value: 1 },
        { label: "植入视频", value: 2 }
      ];
    case 6:
      return [
        { label: "1-60s", value: 1 },
        { label: "60s以上", value: 2 }
      ];
    default: // 默认是平台类型1和2(抖音)的选项
      return [
        { label: "1-20S视频", value: 1 },
        { label: "21-60S视频", value: 2 },
        { label: "60S以上视频", value: 71 },
        { label: "招募任务一口价", value: 100 }
      ];
  }
});
const exportKolList = (isAll = false) => {
  exportKolListApi({
    task_id: taskDetail.value?.task?.id,
    is_all: isAll
  }).then(res => {
    ElMessage({
      message: "导出成功",
      type: "success",
      duration: 5000
    })

    
    const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '达人列表.xlsx';
    a.click();
    URL.revokeObjectURL(url);
  });
}
//添加达人操作
const addKolHandle = () => {
  // 根据平台类型决定显示哪个对话框
  const platformType = taskDetail.value?.task?.platform_type || 1;
  
  if (platformType === 1 || platformType === 2) {
    // 抖音平台使用原有对话框
    addKolDialogVisible.value = true;
    // 在对话框打开后，确保在关闭时计算总毛利率
    nextTick(() => {
      const originalClose = addKolDialogVisible.value.close;
      addKolDialogVisible.value.close = () => {
        originalClose();
        calculateTotalGrossMargin();
      };
    });
  } else {
    // 非抖音平台使用手动添加对话框
    addManualKolDialogVisible.value = true;
  }
};

onMounted(() => {
  searchMedium();
});
//设置建联媒介
function setKol() {
  let changeMendium = medium.value.filter(item => item.id == alliance_personnel.value);
  if (changeMendium.length > 0) {
    const newPersonnelName = changeMendium[0].name;
    const newPersonnelId = changeMendium[0].id;
    // 更新选中的达人列表
    selectionKolList.value = selectionKolList.value.map(item => {
      // 检查该达人是否在 kolList 中
      if (kolList.value.some(kol => kol.platform_uid === item.platform_uid)) {
        // 更新达人信息
        return {
          ...item,
          alliance_personnel: newPersonnelName,
          alliance_personnel_id: newPersonnelId
        };
      }
      return item;
    });
  }
  dialogInformation.value = false;
}
const setMedium = row => {
  let changeMendium = medium.value.filter(item => item.name == row.alliance_personnel);
  row.alliance_personnel_id = changeMendium[0].id;
};

const openDialogInformation = () => {
  let data = kolList.value.map(item => item.platform_uid);
  data = Array.from(new Set(data));
  if (!data.length) {
    ElMessage({
      message: "请选择达人",
      type: "warning",
      duration: 5000
    });
    return;
  }
  dialogInformation.value = true;
};
const taskList = ref([]);

const addTaskForm = ref({
  project_id: "",
  task_name: "",
  promotion_platforms_genres: 1,
  task_type: 1,
  settlement_method: 1
});
//创建新任务
const createTask = () => {
  dialogTaskVisible1.value = true;
};

const closeDrawer = () => {
  dialogTaskVisible1.value = false;
};
const projectList = ref([]);
//远程搜索项目
const remoteMethod1 = query => {
  if (query) {
    loading.value = false;
    searchProjects({ project_name: query }).then(res => {
      projectList.value = res.data.lists;
    });
  } else {
    projectList.value = [];
  }
};
const loading3 = ref(false);
const options = ref([]);
const remoteMethod = query => {
  if (query) {
    loading3.value = true;
    let params = {
      project_name: query,
      page: 1,
      page_size: 10
    };
    searchProjects(params).then(res => {
      loading3.value = false;
      const searchDate = res.data.lists;
      options.value = searchDate;
    });
  } else {
    remoteMethodSelect();
  }
};
const loading2 = ref(false);
const remoteMethodSelect = () => {
  loading2.value = true;
  let params = {
    search: "",
    page: 1,
    page_size: 10
  };
  searchProjects(params).then(res => {
    loading2.value = false;
    const searchDate = res.data.lists;
    options.value = searchDate;
  });
};
const project_id = ref("");
const createTaskConfirm = () => {
  if (addTaskForm.value.project_id && addTaskForm.value.task_name) {
    addTasks(addTaskForm.value).then(res => {
      if (res.code == 990) {
        ElMessage({
          message: "创建成功",
          type: "success",
          duration: 5000
        })
        dialogTaskVisible1.value = false;
        if (project_id.value) {
          taskListFun();
        }
      }
    });
  } else {
    ElMessage({
      message: "请填写项目信息或任务信息",
      type: "warning",
      duration: 5000
    })
  }
};

//项目选择
const projectSelect = () => {
  if (project_id?.value) {
    taskListFun();
    task_id.value = "";
  }
};

//任务选择
const taskListFun = () => {
  mprojectsTaskApi({ project_id: project_id.value }).then(res => {
    taskList.value = res.data ? res.data : [];
  });
};

//创建提报数据
const createReportHandle = () => {
  addSubmitReportVisible.value = true;
};

//选中当前行
const handleCurrentChange = val => {
  isCurrentObj.value = val;
};

//筛选合同
const searchContract = (query) => {
  // 将搜索内容保存到 contract_id
  contract_id.value = query;

  // 调用合同查询接口,传入搜索内容
  contractInformationListFun(taskDetail.value.project.usc_code, query);
};


const tableRowClassName = ({ rowData, rowIndex }) => {
  // ElTableV2的参数格式：{ rowData, rowIndex }
  // 在这里设置条件，如果满足则返回 'red-row' 类
  if (rowData?.collection_status == 3) {
    return "red-row";
  }
    // 检查返点比例是否小于15
  if ( parseFloat(rowData?.process_info?.gross_profit_margin ?? 0) < 15) {
    return "warning-row";
  }

  const matchedKol = selectionKolList.value.find(item => item.platform_uid == rowData?.platform_uid && item.cooperation_type == rowData?.cooperation_type);
  if (matchedKol) {

    if (matchedKol.history_point_lists?.length > 0) {
      // 查找最大返点比例
      const validEntries = matchedKol.history_point_lists.filter(
        entry => entry && typeof entry.rebate_ratio === 'number'
      );

      if (validEntries.length > 0) {
        // 找出最大返点值
        const maxValue = Math.max(...validEntries.map(entry => entry.rebate_ratio));

        // 检查当前返点是否小于历史最大返点
        if (
          rowData?.process_info?.predict_receivable_medium_ratio !== undefined &&
          parseFloat(rowData?.process_info?.predict_receivable_medium_ratio) < parseFloat(maxValue)
        ) {
          return "warning-row";
        }
      }
    }
  }


  return "";
};

const isRowSelectable = (v, index) => {
  // 在这里设置条件，如果满足则返回 true，否则返回 false
  return v?.collection_status == 1;
};

//添加达人
const confirmAddKol = data => {
  if (data.isXTradio == 1) {
    let isCanAddKol = true;
    data.kol_arr.map(item => {
      selectionKolList.value.map(i => {
        if (item.platform_uid == i.platform_uid && item.cooperation_type == i.cooperation_type) {
          isCanAddKol = false;
        }
      });
      if (isCanAddKol) {
        selectionKolList.value.push({
          kol_name: item.kol_name,
          platform_uid: item.platform_uid,
          kol_type: item.kol_type || 1,
          kol_price: item.kol_price ? item.kol_price : 0,
          cooperation_type: item.cooperation_type,
          xingtu_task_name: item.xingtu_task_name,
          kol_num: item.kol_num ? item.kol_num : 1,
          kol_photo: item.kol_photo,
          price_1_20: item.price_1_20,
          price_20_60: item.price_20_60,
          price_60: item.price_60,
          rebate_ratio: item.rebate_ratio ? item.rebate_ratio : 0,
          kol_attributes: 1,
          // last_reviews: {
          //   1: {},
          //   2: {},
          //   3: {}
          // },
          mcnObject: {
            mcn_name: "",
            mcn_short_name: "",
            mcn_id: ""
          },
          mcn_name: "",
          mcn_short_name: "",
          mcn_id: "",
          this_rebate_ratio: 0,
          this_rebate_price: 0,
          contact_information: "",
          collection_status: 1
        });
      } else {
        ElMessage({
          message: "该达人已存在",
          type: "warning",
          duration: 5000
        });
      }
    });
    addKolDialogVisible.value = false;
  } else {
    let addKolData = [];
    data.kol_arr.map(item => {
      let isCanAddKol = true;
      selectionKolList.value.map(i => {
        if (item.platform_uid == i.platform_uid && item.cooperation_type == i.cooperation_type) {
          isCanAddKol = false;
        }
      });
      if (isCanAddKol) {
        selectionKolList.value.push({
          kol_name: "",
          platform_uid: item.platform_uid,
          kol_type: "",
          kol_price: 0,
          cooperation_type: 1,
          xingtu_task_name: "",
          kol_num: 1,
          kol_photo: null,
          rebate_ratio: 0,
          kol_attributes: 1,
          mcnObject: {
            mcn_name: "",
            mcn_short_name: ""
          },
          mcn_name: "",
          mcn_short_name: "",
          this_rebate_ratio: 0,
          this_rebate_price: 0,
          contact_information: "",
          collection_status: 2,
          // last_reviews: {
          //   1: {},
          //   2: {},
          //   3: {}
          // }
        });
        //批量达人添加
        addKolData.push({
          platform_uid: item.platform_uid,
          task_id: taskDetail.value?.task.id,
          project_id: taskDetail.value?.task.project_id,
          cooperation_type: item.cooperation_type
        });
      } else {
        ElMessage({
          message: "该达人已存在",
          type: "warning",
          duration: 5000
        });
      }
    });
    if (addKolData) {
      nonKolStarFun(addKolData, true);
    }
    addKolDialogVisible.value = false;
  }
  // 在函数末尾添加
  calculateTotalGrossMargin();
};

//取消添加达人
const cancelAddKol = () => {
  addSubmitReportVisible.value = false;
  addKolDialogVisible.value = false;
};

//更新达人价格
function updatePrices(data1, data2) {
  const priceMap = {};
  data2.forEach(item => {
    const platformUid = Object.keys(item)[0];
    priceMap[platformUid] = item[platformUid];
  });
  data1.forEach(item => {
    const platformUid = item.platform_uid;
    if (priceMap[platformUid]) {
      // 更新价格
      item.kol_price =
        (item.cooperation_type == 1
          ? priceMap[platformUid].price_1_20
          : item.cooperation_type == 2
            ? priceMap[platformUid].price_20_60
            : item.cooperation_type == 71
              ? priceMap[platformUid].price_60
              : 0) * item.kol_num;
      item.price_1_20 = priceMap[platformUid].price_1_20;
      item.price_20_60 = priceMap[platformUid].price_20_60;
      item.price_60 = priceMap[platformUid].price_60;
      item.kol_name = priceMap[platformUid].kol_name;
      item.kol_photo = priceMap[platformUid].kol_photo;
    }
  });

  return data1;
}

// 获取达人实时刊例价
const getKolPriceHandle = async row => {
  let data = kolList.value.map(item => item.platform_uid);
  data = Array.from(new Set(data));
  if (!data.length) {
    ElMessage({
      message: "请选择达人",
      type: "warning",
      duration: 5000
    });
    return;
  }

  const messageInstance = ElMessage({
    message: "正在获取实时刊例价...",
    type: "info",
    duration: 0
  });
  try {
    let res = await postRealTimePrice({ kols: data });
    if (res?.code == 990) {
      selectionKolList.value = updatePrices(selectionKolList.value, res?.data);
      messageInstance.close();
      ElMessage({
        message: "获取成功",
        type: "success",
        duration: 5000
      });
    }
  } catch (error) {
    messageInstance.close();
    ElMessage({
      message: "获取失败",
      type: "error",
      duration: 5000
    });
  }
};
//取消
const cancelWriteOrder = () => {
  closeCurrentTab();
  router.push({
    path: "/business/task"
  });
};
//kolListDel 删除
const kolListDel = (platform_uid, cooperation_type) => {
  // 使用 platform_uid 和 cooperation_type 组合来定位具体要删除的达人
  let index = selectionKolList.value.findIndex(
    item => item.platform_uid === platform_uid && item.cooperation_type === cooperation_type
  );

  if (index !== -1) {
    // 在删除前检查该达人是否还有其他合作形式存在
    const hasOtherCooperation = selectionKolList.value.some(
      item => item.platform_uid === platform_uid && item.cooperation_type !== cooperation_type
    );

    // 删除指定的达人数据
    selectionKolList.value.splice(index, 1);
    localStorage.setItem("kolList", JSON.stringify(selectionKolList.value));

    // 如果该达人没有其他合作形式了，则从 kolList 中也移除
    if (!hasOtherCooperation) {
      const kolIndex = kolList.value.findIndex(
        item => item.platform_uid === platform_uid && item.cooperation_type === cooperation_type
      );
      if (kolIndex !== -1) {
        kolList.value.splice(kolIndex, 1);
      }
    }

    ElMessage({
      message: "删除成功",
      type: "success",
      duration: 5000
    });
  }
};

//切换服务类型
const changeType = row => {
  const platformType = taskDetail.value?.task?.platform_type || 1;
  
  // 根据平台类型和合作形式计算价格
  if (platformType === 1 || platformType === 2) {
    // 抖音平台
    if (row.cooperation_type == 1) {
      row.kol_price = row.price_1_20 * row.kol_num;
    } else if (row.cooperation_type == 2) {
      row.kol_price = row.price_20_60 * row.kol_num;
    } else if (row.cooperation_type == 71) {
      row.kol_price = row.price_60 * row.kol_num;
    }
  } else if (platformType === 3) {
    // 平台类型3 - 图文笔记/视频笔记
    if (row.cooperation_type == 1) { // 图文笔记
      row.kol_price = (row.price_1_20 || row.kol_price) * row.kol_num;
    } else if (row.cooperation_type == 2) { // 视频笔记
      row.kol_price = (row.price_20_60 || row.kol_price) * row.kol_num;
    }
  } else if (platformType === 6) {
    // 平台类型6 - 腾讯互选 - 1-60s/60s以上
    // 对于腾讯互选，不基于时长重新计算价格，维持原价格
    // 但是根据数量计算总价
    row.kol_price = row.kol_price * row.kol_num;
  }
  
  // 同步更新 process_info.kol_base_price
  if (row.process_info) {
    row.process_info.kol_base_price = row.kol_price;
  }
  
  row.this_rebate_price = ((row.this_rebate_ratio / 100) * row.kol_price).toFixed(2);
};

// 更新B站服务类型及相关价格
const updateBiliServiceType = row => {
  // 确保process_info存在
  if (!row.process_info) {
    row.process_info = {};
  }
  
  // 确保bili_service_type是数字类型
  const serviceType = Number(row.process_info.bili_service_type);
  row.process_info.bili_service_type = serviceType;
  
  // 获取基础价格 - 优先使用kol_base_price，如果没有则使用kol_price
  const basePrice = Number(row.process_info?.kol_base_price) || Number(row.kol_price) || 0;
  const licensingFee = Number(row.process_info?.kol_licensing_fee) || 0;

  // B站根据服务类型计算不同的服务费率
  const serviceFeeRate = serviceType === 1 ? 1.07 : 1.05;  // 1=个人UP主(7%), 2=签约UP主(5%)

  // 计算下单总金额 (B站特殊处理)
  const starPrice = (basePrice + licensingFee) * serviceFeeRate;
  row.process_info.star_price = Number(starPrice.toFixed(2));

  
  // 如果没有手动改价，更新实际下单价格
  if (row.process_info.change_status === 0 || !row.process_info.change_status) {
    row.process_info.actual_amount_price = Number(starPrice.toFixed(2));
  }
  
  // 检查是否为抖音招募任务
  const platformType = taskDetail.value?.task?.platform_type || 1;
  const taskType = taskDetail.value?.task?.task_type || 1;
  const isDouyinRecruitmentTask = (platformType === 1 || platformType === 2) && taskType === 2;

  if (!isDouyinRecruitmentTask) {
    // 非抖音招募任务：使用原有的计算逻辑
    // 重新计算预估成本
    if (row.process_info.predict_receivable_medium_price) {
      const mediaPriceRebate = Number(row.process_info.predict_receivable_medium_price) || 0;
      row.process_info.predict_cost = Number((row.process_info.actual_amount_price - mediaPriceRebate).toFixed(2));
    }

    // 重新计算预估应收客户款
    const customerRebate = Number(row.process_info.customer_rebate) || 0;
    const customerService = Number(row.process_info.customer_service_price) || 0;
    row.process_info.predict_receivable_customer_price = Number((row.process_info.actual_amount_price - customerRebate + customerService).toFixed(2));

    // 重新计算毛利
    if (row.process_info.predict_receivable_customer_price && row.process_info.predict_cost) {
      const grossProfit = row.process_info.predict_receivable_customer_price - row.process_info.predict_cost;
      row.process_info.gross_profit = Number(grossProfit.toFixed(2));

      // 重新计算毛利率
      if (row.process_info.predict_receivable_customer_price > 0) {
        const grossProfitMargin = (grossProfit / row.process_info.predict_receivable_customer_price) * 100;
        row.process_info.gross_profit_margin = Number(grossProfitMargin.toFixed(2));
      }
    }
  }
  // 抖音招募任务：不进行计算，保持接口返回的值
  
  // 更新计算总毛利率
  calculateTotalGrossMargin();
};

const setDefaultSelection = e => {
  // ElTableV2 不支持 toggleRowSelection，直接更新 selectedRows
  nextTick(() => {
    const defaultSelected = e.map(({ id, cooperation_type }) => {
      return selectionKolList.value.find(item =>
        item.platform_uid === id &&
        item.cooperation_type === cooperation_type &&
        item.collection_status == 1
      );
    }).filter(Boolean);

    selectedRows.value = defaultSelected;
    handleSelectionChange(selectedRows.value);
  });
};

// 计算初始化下单信息
const initOrderInfo = v => {
  if (!v.process_info) return undefined;

  // 检查是否为抖音招募任务
  const platformType = taskDetail.value?.task?.platform_type || 1;
  const taskType = taskDetail.value?.task?.task_type || 1;
  const isDouyinRecruitmentTask = (platformType === 1 || platformType === 2) && taskType === 2;

  // 保留原有的 process_info 数据，特别是 _v1 字段
  const originalProcessInfo = v.process_info || {};

  if (isDouyinRecruitmentTask) {
    // 抖音招募任务：直接返回原有数据，不进行计算
    return {
      // 保留所有原有字段
      ...originalProcessInfo,
      // 确保基础字段存在
      kol_base_price: originalProcessInfo.kol_base_price || v.kol_price, //平台刊例价
      bili_service_type: originalProcessInfo.bili_service_type || 1 //B站服务类型，默认为个人UP主
    };
  }

  // 非抖音招募任务：使用原有的计算逻辑
  // 处理B站服务费 - 根据服务类型来确定服务费率
  let serviceFeeRate = 1.1; // 默认服务费率
  if (taskDetail.value?.task?.platform_type === 5) { // B站
    // 1=个人UP主(7%), 2=签约UP主(5%)
    serviceFeeRate = v.process_info?.bili_service_type === 1 ? 1.07 : 1.05;
  }

  // 计算下单总金额=(达人裸价+达人授权费裸价)*服务费率
  // 优先使用原始的 kol_base_price，如果没有才使用 v.kol_price
  const basePrice = v.process_info?.kol_base_price || v.kol_price;
  const starPrice = v.process_info?.change_status == 1
    ? v.process_info?.actual_amount_price
    : (basePrice + v.process_info?.kol_licensing_fee) * serviceFeeRate;

  //预估应收客户款
  const predict_receivable_customer_price =
    starPrice -
    v.process_info?.customer_rebate +
    v.process_info?.customer_service_price;

  //预估成本
  const predict_cost =
    starPrice - v.process_info?.predict_receivable_medium_price;

  return {
    // 保留所有原有字段
    ...originalProcessInfo,
    // 覆盖需要重新计算的字段
    actual_amount_price: starPrice, //实际下单总金额
    change_status: v.process_info?.change_status, //是否改价
    customer_rebate: v.process_info?.customer_rebate, //客户返佣
    customer_service_price: v.process_info?.customer_service_price, //客户服务费
    gross_profit: predict_receivable_customer_price - predict_cost, //毛利
    gross_profit_margin: v.process_info?.gross_profit_margin || ((predict_receivable_customer_price - predict_cost) / predict_receivable_customer_price * 100) || 0, //毛利率
    original_rebate_ratio: v.process_info?.original_rebate_ratio, //原始返点比例
    img_url: v.process_info?.img_url, //图片
    // 优先保留原始的 kol_base_price，如果没有才使用 v.kol_price
    kol_base_price: v.process_info?.kol_base_price || v.kol_price, //平台刊例价
    kol_licensing_fee: v.process_info?.kol_licensing_fee, //达人授权费(不含服务费)
    order_status: v.process_info?.order_status, //订单状态
    other_notes: v.process_info?.other_notes, //备注
    predict_cost: predict_cost, //预估成本
    predict_receivable_customer_price: predict_receivable_customer_price, //预估应收客户款
    predict_receivable_medium_price: v.process_info?.predict_receivable_medium_price, //预估应收媒体返点金额
    predict_receivable_medium_ratio: v.process_info?.predict_receivable_medium_ratio ||
      (v.process_info?.predict_receivable_medium_price && (v.process_info?.kol_base_price || v.kol_price)
        ? ((Number(v.process_info.predict_receivable_medium_price) / Number(v.process_info?.kol_base_price || v.kol_price)) * 100).toFixed(2)
        : 0), //预估应收媒体返点比例 - 优先使用原始的kol_base_price
    process_kol_id: v.process_info?.process_kol_id, //流程达人id
    special_rebate_amount: v.process_info?.special_rebate_amount, //特殊返点
    star_price: starPrice, //下单总金额=(达人裸价+达人授权费裸价)*1.1
    status: v.process_info?.status, //状态
    xingtu_task_name: v.xingtu_task_name, //星图任务名称
    bili_service_type: v.process_info?.bili_service_type || 1 //B站服务类型，默认为个人UP主
  };
};

const task_status = ref(0);

const closeCurrentTab = () => {
  tabStore.removeTabs(route.fullPath);
};

//通过任务详情
const taskProcessKolDetailFun = id => {
  pageLoading.value = true; // 开始加载
  taskProcessKolDetail({ task_id: id }).then(res => {
    if (res.data == null) {
      ElMessage({
        message: res.msg,
        type: "error",
        duration: 5000
      });
      pageLoading.value = false; // 加载失败时关闭loading
      closeCurrentTab();
      router.push({
        path: "/business/task",
        query: {
          key: "4-3",
          task_id: route.query.id
        }
      });
      return;
    }

    if (route.query.id) {
      selectionKolList.value = [
        ...new Map(
          // Use a proper composite key format like "uid:type"
          [...selectionKolList.value, ...res.data?.kols].map(item => [`${item.platform_uid}:${item.cooperation_type}`, item])
        ).values()
      ];
    } else {
      let kol_data = updateKolList();
      kol_data = [
        ...new Map(
          [
            ...kol_data,
            ...res.data.kols.map(v => {
              return v;
            })
          // Use a proper composite key format like "uid:type"
          ].map(item => [`${item.platform_uid}:${item.cooperation_type}`, item])
        ).values()
      ];
      selectionKolList.value = kol_data;
    }

    // Get platform type from task data
    const platformType = res.data.task.platform_type || 1;
    
    let selectionKolList_let = selectionKolList.value.map(item => {
      // Only recalculate prices for Douyin platforms (types 1 and 2)
      // For other platforms, preserve the original kol_price
      if (platformType === 1 || platformType === 2) {
        // For Douyin platforms, calculate price based on specific duration prices
        return {
          ...item,
          kol_price:
            (item.cooperation_type == 1
              ? item.price_1_20
              : item.cooperation_type == 2
                ? item.price_20_60
                : item.cooperation_type == 71
                  ? item.price_60
                  : 0) * item.kol_num,
          price_1_20: item.price_1_20,
          price_20_60: item.price_20_60,
          price_60: item.price_60
        };
      } else {
        // For non-Douyin platforms, preserve the original kol_price
        return {
          ...item,
          // Keep the original kol_price or use process_info.kol_base_price as fallback
          kol_price: item.kol_price || item.process_info?.kol_base_price || 0,
          price_1_20: item.price_1_20,
          price_20_60: item.price_20_60,
          price_60: item.price_60
        };
      }
    });
    
    selectionKolList.value = selectionKolList_let.map(item => {
      // 确保 order_info 是一个单独的对象，不是 process_info 的一部分
      const orderInfo = item.process_info?.order_info || item.order_info || {};
      
      // Create the base object with process_info
      const updatedItem = {
        ...item,
        process_info: initOrderInfo(item)
      };
      
      
      // Only include order_info if task's order_method is not 1
      if (res.data.task.order_method !== 1) {
        // 确保 order_info 是和 process_info 平级的
        updatedItem.order_info = orderInfo;
      }
      
      return updatedItem;
    });

    setTimeout(() => {
      setDefaultSelection(
        selectionKolList.value.map(item => {
          return { id: item.platform_uid, cooperation_type: item.cooperation_type };
        })
      );
    }, 200);
    
    taskDetail.value = res.data;
    task_status.value = res.data.task.status;

    // 如果是招募任务模式，初始化招募信息
    if (res.data.task.task_type === 2) {
      initRecruitmentInfo(res.data);

      // 如果是抖音平台的招募任务，初始化组件信息
      if (res.data.task.platform_type === 1) {
        initComponentInfo(res.data);
      }
    }

    if (taskDetail.value?.process_before?.customer_code) {
      contract_id.value = taskDetail.value?.process_before?.customer_code;
      isCurrentObj.value = taskDetail.value?.process_before;
    }
    contractInformationListFun(res.data.project.usc_code);
    if (selectionKolList.value && selectionKolList.value.length > 0) {
      selectionKolList.value.map(item => {
        item.mcnObject = {
          mcn_name: item.mcn_name ? item.mcn_name : "",
          mcn_short_name: item.mcn_short_name ? item.mcn_short_name : "",
          mcn_id: item.mcn_id ? item.mcn_id : ""
        };
        item.kol_attributes = item.kol_attributes;
      });
    }
    
    // 修复所有达人价格
    fixKolPrices();

    // 在函数末尾添加
    calculateTotalGrossMargin();

    // 初始化过滤列表
    handleTalentSearch();

    // 数据加载完成，关闭骨架屏
    pageLoading.value = false;
  }).catch(error => {
    pageLoading.value = false; // 出错时也要关闭loading
  });
};
const task_id = ref("");
const percentage = ref(0);
const progressLevel = ref(false);
const fileIsUploade = ref(false);
//上传达人
const onSuccessLoad = data => {
  percentage.value = 0;
  dialogTaskVisible.value = false;
  progressLevel.value = true;
  fileIsUploade.value = false;
  
  // 获取平台类型从任务信息
  const platformType = taskDetail.value?.task?.platform_type || 1; // 默认为1（抖音）如果任务信息中没有
  
  let formData = new FormData();
  formData.append("file", data.raw);
  formData.append("task_id", route.query.id || taskDetail.value?.task?.id || task_id.value);
  formData.append("platform_type", platformType);
  
  // 显示适当的信息，基于平台类型
  // const needCollection = platformType === 1 || platformType === 2;
  // const statusMessage = needCollection ? "数据解析中，将进行达人信息采集..." : "数据解析中，非抖音平台无需进行达人信息采集";
  
  // ElMessage({
  //   message: statusMessage,
  //   type: "info",
  //   duration: 5000
  // });
  
  // 修改上传达人API调用，仅获取数据不直接存入后端
  uploadKolApiNew(formData)
    .then(res => {
      if (res.code == 990) {
        fileList.value = [];
        percentage.value = 100;
        // 不再调用taskProcessKolDetailFun从后端刷新数据
        // 数据将通过socket事件获取并在前端处理
      } else {
        fileList.value = [];
        progressLevel.value = false;
        ElMessage({
          message: res.msg || "导入失败",
          type: "error",
          duration: 5000
        });
      }
      fileIsUploade.value = true;
    })
    .catch(error => {
      fileList.value = [];
      progressLevel.value = false;
      ElMessage({
        message: "导入发生错误",
        type: "error",
        duration: 5000
      });
    });
};

watch(
  () => percentage.value,
  () => {
    // 不再根据进度条完成刷新后端数据
    // if (percentage.value == 100) {
    //   taskProcessKolDetailFun(route.query.id || taskDetail.value?.task.id || task_id.value);
    // }
  }
);

//查询建联人员
const getRoleUsersFun = () => {
  getRoleUsersFirstApi().then(res => {
    roleUsers1.value = res.data;
  });
  getRoleUsersSecondApi().then(res => {
    roleUsers2.value = res.data;
  });
  getRoleUsersThirdApi().then(res => {
    roleUsers3.value = res.data;
  });
};
const mcnListFun = () => {
  mcnListApi({
    page: 1,
    page_size: 1000
  }).then(res => {
    mcnList.value = res.data;
  });
};
//机构选中
const handleMcnChange = (value, row) => {
    row.mcnObject = {
      mcn_name: value.mcn_name ? value.mcn_name : "",
      mcn_short_name: value.mcn_short_name ? value.mcn_short_name : "",
      mcn_id: value.id ? value.id : "",
      mcnNameList: [value.mcn_name]
    };
    // 同步更新顶层MCN属性
    row.mcn_name = value.mcn_name || "";
    row.mcn_short_name = value.mcn_short_name || "";
    row.mcn_id = value.id || "";
    row.contact_information = value.mcn_contact_person;
    // 设置为机构达人
    row.kol_attributes = 2;
};

//通过usc_code查询合同
const contractInformationListFun = (usc_code, contract_id) => {
  contractInformationList({
    usc_code: usc_code,
    contract_id: contract_id, // 使用 contract_id 进行搜索
    page: contractPage.value ? contractPage.value : 1,
    page_size: 999
  }).then(res => {
    customerUscList.value = res.data.lists;
    total.value = res.data.count;
  });
};
const cooperation_type = ref(1);
//编辑
const handleConnectInfo = row => {
  console.log(row, "row");
  kol_id.value = row.platform_uid;
  cooperation_type.value = row.cooperation_type;

  // Find the most up-to-date version of this KOL from selectionKolList
  const updatedKol = selectionKolList.value.find(
    item => item.platform_uid === row.platform_uid && item.cooperation_type === row.cooperation_type
  );

  // Use the updated KOL data if found, otherwise use the original row
  let currentRow = updatedKol || row;

  // For recruitment tasks, ensure complete data structure
  const platformType = Number(taskDetail.value?.task?.platform_type);
  const taskType = Number(taskDetail.value?.task?.task_type);
  const isDouyinRecruitmentTask = (platformType === 1 || platformType === 2) && taskType === 2;

  if (isDouyinRecruitmentTask) {
    // Merge data from original row to ensure completeness
    currentRow = {
      ...row,
      ...currentRow,
      // Ensure process_info is complete
      process_info: {
        ...(row.process_info || {}),
        ...(currentRow.process_info || {})
      },
      // Ensure ext is complete
      ext: {
        ...(row.ext || {}),
        ...(currentRow.ext || {})
      }
    };

  }
  
  // 设置完整的达人信息，确保对话框可以访问所有属性
  rowItem.value = currentRow;

  // 特殊处理B站服务类型 - 在平台类型为5(B站)时
  if (taskDetail.value?.task?.platform_type === 5) {
    // 确保process_info存在
    if (!rowItem.value.process_info) {
      rowItem.value.process_info = {};
    }
    
    // 优先使用根级别的bili_service_type
    if (currentRow.bili_service_type !== undefined) {
      rowItem.value.process_info.bili_service_type = Number(currentRow.bili_service_type);
    }
    // 其次使用process_info中的bili_service_type
    else if (currentRow.process_info?.bili_service_type !== undefined) {
      rowItem.value.process_info.bili_service_type = Number(currentRow.process_info.bili_service_type);
    }
    // 然后使用order_info.ext中的bili_service_type
    else if (currentRow.order_info?.ext?.bili_service_type !== undefined) {
      rowItem.value.process_info.bili_service_type = Number(currentRow.order_info.ext.bili_service_type);
    }
    // 最后使用ext中的bili_service_type
    else if (currentRow.ext?.bili_service_type !== undefined) {
      rowItem.value.process_info.bili_service_type = Number(currentRow.ext.bili_service_type);
    }
    // 如果都没有，设置默认值
    else {
      rowItem.value.process_info.bili_service_type = 1;
    }
  }
  
  dialogType.value = "edit";  // 修改为edit，表示这是编辑操作
  
  // 不再需要单独设置processInfo，因为完整的达人数据已经在rowItem中了
  // 但为了兼容性，我们仍然设置processInfo
  processInfo.value = currentRow.process_info || {};

  dialogVisible.value = true;
};
//查看信息
const openConnectInfo = value => {
  kol_id.value = value.platform_uid;
  dialogType.value = "info";
  
  // Find the most up-to-date version of this KOL from selectionKolList
  const updatedKol = selectionKolList.value.find(
    item => item.platform_uid === value.platform_uid && item.cooperation_type === (value.cooperation_type || 1)
  );
  
  // Use the updated KOL data if found, otherwise use the original value
  const currentKol = updatedKol || value;
  
  // 设置完整的达人信息，确保对话框可以访问所有属性
  rowItem.value = currentKol;
  
  // 特殊处理B站服务类型 - 在平台类型为5(B站)时
  if (taskDetail.value?.task?.platform_type === 5) {
    // 确保process_info存在
    if (!rowItem.value.process_info) {
      rowItem.value.process_info = {};
    }
    
    // 优先使用根级别的bili_service_type
    if (currentKol.bili_service_type !== undefined) {
      rowItem.value.process_info.bili_service_type = Number(currentKol.bili_service_type);
    }
    // 其次使用process_info中的bili_service_type
    else if (currentKol.process_info?.bili_service_type !== undefined) {
      rowItem.value.process_info.bili_service_type = Number(currentKol.process_info.bili_service_type);
    }
    // 然后使用order_info.ext中的bili_service_type
    else if (currentKol.order_info?.ext?.bili_service_type !== undefined) {
      rowItem.value.process_info.bili_service_type = Number(currentKol.order_info.ext.bili_service_type);
    }
    // 最后使用ext中的bili_service_type
    else if (currentKol.ext?.bili_service_type !== undefined) {
      rowItem.value.process_info.bili_service_type = Number(currentKol.ext.bili_service_type);
    }
    // 如果都没有，设置默认值
    else {
      rowItem.value.process_info.bili_service_type = 1;
    }
  }
  
  // 为了兼容性，仍然设置processInfo
  processInfo.value = currentKol.process_info || {};
  
  dialogVisible.value = true;
};

//填写下单信息
const confirmAddKolInfo = data => {

  // 检查是否为抖音招募任务
  const platformType = Number(taskDetail.value?.task?.platform_type);
  const taskType = Number(taskDetail.value?.task?.task_type);
  const isDouyinRecruitmentTask = (platformType === 1 || platformType === 2) && taskType === 2;

  // Update selectionKolList
  selectionKolList.value = selectionKolList.value.map(item => {
    // Fix the comparison to check both values independently
    if (item.platform_uid === kol_id.value && item.cooperation_type === cooperation_type.value) {

      if (isDouyinRecruitmentTask) {
        // 抖音招募任务：只有特定字段在根级别，其他字段在process_info中
        const updatedItem = {
          ...item,
          // 基础信息更新
          kol_name: data.kol_name || item.kol_name,
          platform_uid: data.platform_uid || item.platform_uid,
          cooperation_type: 1, // 招募任务固定为1
          kol_price: data.kol_price || item.kol_price,
          xingtu_task_name: data.xingtu_task_name || item.xingtu_task_name, // 确保任务名称被更新
          xingtu_project_name: data.xingtu_project_name || item.xingtu_project_name, // 确保项目名称被更新
          alliance_personnel: data.alliance_personnel || item.alliance_personnel,
          alliance_personnel_id: data.alliance_personnel_id || item.alliance_personnel_id,
          contact_information: data.contact_information || item.contact_information,
          mcnObject: data.mcnObject || item.mcnObject,
          mcn_name: data.mcn_name || item.mcn_name,
          mcn_short_name: data.mcn_short_name || item.mcn_short_name,
          mcn_id: data.mcn_id || item.mcn_id,

          // 招募任务特定字段（放在根级别）
          is_xingxiao_kol: data.is_xingxiao_kol !== undefined ? data.is_xingxiao_kol : item.is_xingxiao_kol,
          kol_fans_num: data.kol_fans_num !== undefined ? Number(data.kol_fans_num) || 0 : (Number(item.kol_fans_num || item.ext?.kol_fans_num) || 0),
          show_customer_fee: data.show_customer_fee !== undefined ? Number(data.show_customer_fee) || 0 : (Number(item.show_customer_fee || item.ext?.show_customer_fee) || 0),
          customer_rebate_ratio: data.customer_rebate_ratio !== undefined ? Number(data.customer_rebate_ratio) || 0 : (Number(item.customer_rebate_ratio || item.ext?.customer_rebate_ratio) || 0),
          provider_price_exclue_service_fee: data.provider_price_exclue_service_fee !== undefined ? Number(data.provider_price_exclue_service_fee) || 0 : (Number(item.provider_price_exclue_service_fee || item.ext?.provider_price_exclue_service_fee) || 0),
          provider_price: data.provider_price !== undefined ? Number(data.provider_price) || 0 : (Number(item.provider_price || item.ext?.provider_price) || 0),

          // 招募任务利润信息字段（放在根级别）
          kol_licensing_fee: data.kol_licensing_fee !== undefined ? data.kol_licensing_fee : item.kol_licensing_fee,
          predict_receivable_customer_price: data.predict_receivable_customer_price !== undefined ? data.predict_receivable_customer_price : item.predict_receivable_customer_price,
          gross_profit: data.gross_profit !== undefined ? data.gross_profit : item.gross_profit,
          gross_profit_margin: data.gross_profit_margin !== undefined ? data.gross_profit_margin : item.gross_profit_margin,
          // v1 fields for display/echo
          predict_receivable_customer_price_v1: data.predict_receivable_customer_price !== undefined ? data.predict_receivable_customer_price : item.predict_receivable_customer_price_v1,
          gross_profit_v1: data.gross_profit !== undefined ? data.gross_profit : item.gross_profit_v1,
          gross_profit_margin_v1: data.gross_profit_margin !== undefined ? data.gross_profit_margin : item.gross_profit_margin_v1,
          change_status: data.change_status !== undefined ? data.change_status : item.change_status,
          customer_rebate: data.customer_rebate !== undefined ? data.customer_rebate : item.customer_rebate,
          customer_service_price: data.customer_service_price !== undefined ? data.customer_service_price : item.customer_service_price,
          predict_receivable_medium_ratio: data.predict_receivable_medium_ratio !== undefined ? data.predict_receivable_medium_ratio : item.predict_receivable_medium_ratio,
          expect_publish_month: data.expect_publish_month !== undefined ? data.expect_publish_month : (item.expect_publish_month || item.ext?.expect_publish_month || item.expect_release_time),

          // 返点信息
          this_rebate_ratio: data.this_rebate_ratio || item.this_rebate_ratio || 0,
          this_rebate_price: data.this_rebate_price || item.this_rebate_price || 0,

          // 更新时间
          updated_at: new Date().getTime(),

          // 保持原有的一些字段
          process_kol_id: item.process_kol_id,
          collection_status: item.collection_status,
          collection_msg: item.collection_msg,

          // 合并process_info对象，确保_v1字段也被更新
          process_info: {
            ...(item.process_info || {}),
            ...(data.process_info || {}),
            // 确保_v1字段在process_info中也被更新 - 用于下次回显
            predict_receivable_customer_price_v1: data.predict_receivable_customer_price !== undefined ? data.predict_receivable_customer_price : (item.process_info?.predict_receivable_customer_price_v1 || item.predict_receivable_customer_price_v1),
            gross_profit_v1: data.gross_profit !== undefined ? data.gross_profit : (item.process_info?.gross_profit_v1 || item.gross_profit_v1),
            gross_profit_margin_v1: data.gross_profit_margin !== undefined ? data.gross_profit_margin : (item.process_info?.gross_profit_margin_v1 || item.gross_profit_margin_v1),
            // 同时更新非_v1字段
            predict_receivable_customer_price: data.predict_receivable_customer_price !== undefined ? data.predict_receivable_customer_price : (item.process_info?.predict_receivable_customer_price),
            gross_profit: data.gross_profit !== undefined ? data.gross_profit : (item.process_info?.gross_profit),
            gross_profit_margin: data.gross_profit_margin !== undefined ? data.gross_profit_margin : (item.process_info?.gross_profit_margin)
          },

          // 合并ext对象，确保新数据不被覆盖
          ext: {
            ...(item.ext || {}),
            ...(data.ext || {})
          }
        };

        return updatedItem;
      } else {
        // 非招募任务：保持原有的数据结构处理逻辑
        const { process_info, order_info, ...basicInfo } = data;

        // 创建新的 process_info 对象
        const newProcessInfo = {
          ...(item.process_info || {}),
          ...process_info,
          // 确保 kol_base_price 与 kol_price 一致
          kol_base_price: data.kol_price, // 使用新的kol_price
          // 处理特殊字段
          img_url: process_info?.img_url === 0 ? "" : process_info?.img_url,
          other_notes: process_info?.other_notes === 0 ? "" : process_info?.other_notes,
          updated_at: new Date().getTime()
        };

        // 确保数值类型正确
        Object.keys(newProcessInfo).forEach(key => {
          if (key !== "img_url" && key !== "other_notes") {
            newProcessInfo[key] = Number(newProcessInfo[key]) || 0;
          }
        });

        // 明确处理MCN机构信息
        const updatedItem = {
          ...item,
          ...basicInfo, // 添加基础信息的更新
          xingtu_task_name: data.xingtu_task_name, // 确保任务名称被更新
          xingtu_project_name: data.xingtu_project_name, // 确保项目名称被更新
          process_info: newProcessInfo,
          // 明确设置MCN相关字段
          mcnObject: data.mcnObject ? { ...data.mcnObject } : item.mcnObject,
          mcn_name: data.mcn_name || item.mcn_name,
          mcn_short_name: data.mcn_short_name || item.mcn_short_name,
          mcn_id: data.mcn_id || item.mcn_id,
          kol_attributes: data.kol_attributes || item.kol_attributes, // 确保达人类型正确保存
          // Also update this_rebate_ratio for consistency
          this_rebate_ratio: newProcessInfo.predict_receivable_medium_ratio
        };

        // Only include order_info field if order_method is not 1
        if (taskDetail.value?.task?.order_method !== 1) {
          updatedItem.order_info = order_info;
        }

        return updatedItem;
      }
    }
    return item;
  });

  // Also update kolList if it exists and contains this KOL
  if (kolList.value && kolList.value.length > 0) {
    kolList.value = kolList.value.map(item => {
      // Also fix this comparison
      if (item.platform_uid === kol_id.value && item.cooperation_type === cooperation_type.value) {
        // Get the updated item from selectionKolList
        const updatedItem = selectionKolList.value.find(
          sel => sel.platform_uid === item.platform_uid && sel.cooperation_type === item.cooperation_type
        );
        
        if (updatedItem) {
          // Simply return the full updated item to ensure all fields are updated
          return { ...updatedItem };
        }
      }
      return item;
    });
  }

  // Show success message
  ElMessage({
    message: '编辑成功',
    type: 'success',
    duration: 5000
  });

  // Call isNeedReviewFun to immediately update the review status based on new data
  isNeedReviewFun();
  dialogVisible.value = false;
  
  // 在函数末尾添加总毛利率计算
  calculateTotalGrossMargin();
};
//取消填写下单信息
const cancelWrite = () => {
  dialogVisible.value = false;
};

//添加非星推达人
const nonKolStarFun = async (data, showMessage = false) => {
  if (!data.length) return;
  
  // 获取平台类型
  const platformType = taskDetail.value?.task?.platform_type || 1;
  
  // 只有当平台类型为1或2时才进行采集
  if (!(platformType === 1 || platformType === 2)) {
    
    // 对于非采集平台，将所有待采集达人的状态设置为已完成
    selectionKolList.value.forEach(item => {
      if (item.collection_status === 2) {
        item.collection_status = 1; // 设置为已完成
      }
    });
    
    // 同步到kolList并返回
    syncKolList();
    calculateTotalGrossMargin();
    return;
  }

  try {
    const res = await nonStarKolApi(data);
    if (!res.data?.length) return;

    let completedCount = 0;
    res.data.forEach(kol => {
      if (kol.collection_status !== 2) {
        completedCount++;
      }

      // 查找并更新达人信息
      const index = selectionKolList.value.findIndex(
        item => item.platform_uid === kol.platform_uid &&
               item.cooperation_type === kol.cooperation_type
      );

      if (index !== -1) {
        const item = selectionKolList.value[index];
        
        // 更新状态和基础信息
        item.collection_status = kol.collection_status;
        item.collection_msg = kol.collection_msg;
        
        // 只有在采集成功时更新价格和图片信息
        if (kol.collection_status === 1) {
          item.price_1_20 = kol.price_1_20 || 0;
          item.price_20_60 = kol.price_20_60 || 0;
          item.price_60 = kol.price_60 || 0;
          item.kol_name = kol.kol_name || item.kol_name;
          item.kol_photo = kol.kol_photo || item.kol_photo;
          item.process_kol_id = kol.process_kol_id;

          // 处理采集返回的 ext 对象中的招募任务字段
          if (kol.ext && Object.keys(kol.ext).length > 0) {
            // 将 ext 对象中的字段合并到达人对象的根级别
            Object.keys(kol.ext).forEach(key => {
              if (kol.ext[key] !== undefined && kol.ext[key] !== null) {
                item[key] = kol.ext[key];
              }
            });

            // 确保 ext 对象也被保存，以便后续使用
            if (!item.ext) {
              item.ext = {};
            }
            Object.assign(item.ext, kol.ext);
          }

          // 根据合作类型更新价格
          item.kol_price = calculateKolPrice(kol, item.cooperation_type, item.kol_num);
          
          // 更新process_info中的kol_base_price以保持一致
          if (item.process_info) {
            item.process_info.kol_base_price = item.kol_price;

            // 重新计算毛利率相关数据
            const processInfo = item.process_info;
            // 优先使用kol_base_price，如果没有则使用kol_price
            const kolPrice = Number(processInfo.kol_base_price) || Number(item.kol_price) || 0;
            const kolLicensingFee = Number(processInfo.kol_licensing_fee) || 0;
            const customerRebate = Number(processInfo.customer_rebate) || 0;
            const customerServicePrice = Number(processInfo.customer_service_price) || 0;
            const predictReceivableMediumPrice = Number(processInfo.predict_receivable_medium_price) || 0;

            // 计算实际下单总金额 (下单总金额)
            let actualAmountPrice;
            if (processInfo.change_status === 1) {
              // 如果改价，使用实际下单金额
              actualAmountPrice = Number(processInfo.actual_amount_price) || 0;
            } else {
              // 根据平台类型计算下单总金额
              if (platformType === 5) { // B站平台
                // B站根据服务类型计算：1=个人UP主(7%), 2=签约UP主(5%)
                const serviceFeeRate = processInfo.bili_service_type === 1 ? 1.07 : 1.05;
                actualAmountPrice = (kolPrice + kolLicensingFee) * serviceFeeRate;
              } else { // 其他平台
                // 下单总金额=(达人裸价+达人授权费裸价)*1.1
                actualAmountPrice = (kolPrice + kolLicensingFee) * 1.1;
              }
            }

            // 计算预估应收客户款
            const predictReceivableCustomerPrice = actualAmountPrice - customerRebate + customerServicePrice;

            // 计算预估成本
            const predictCost = actualAmountPrice - predictReceivableMediumPrice;

            // 计算毛利
            const grossProfit = predictReceivableCustomerPrice - predictCost;

            // 计算毛利率
            let grossProfitMargin = 0;
            if (predictReceivableCustomerPrice > 0) {
              grossProfitMargin = (grossProfit / predictReceivableCustomerPrice) * 100;
            }

            // 更新process_info
            processInfo.actual_amount_price = actualAmountPrice;
            processInfo.star_price = actualAmountPrice;
            processInfo.predict_receivable_customer_price = predictReceivableCustomerPrice;
            processInfo.predict_cost = predictCost;
            processInfo.gross_profit = grossProfit;
            processInfo.gross_profit_margin = parseFloat(grossProfitMargin.toFixed(2));

            // 计算预估应收媒体返点比例 - 使用平台刊例价而不是实际下单金额
            if (kolPrice > 0) {
              processInfo.predict_receivable_medium_ratio = (predictReceivableMediumPrice / kolPrice) * 100;
            }

            // 计算原返点比例：(刊例价 - 实际下单价(含服务费)/1.05 + 预估媒体返点金额) / 刊例价 * 100%
            if (kolPrice > 0) {
              const actualOrderPrice = actualAmountPrice / 1.05; // 实际下单价(含服务费)/1.05
              const originalRebateRatio = ((kolPrice - actualOrderPrice + predictReceivableMediumPrice) / kolPrice) * 100;
              processInfo.original_rebate_ratio = parseFloat(originalRebateRatio.toFixed(2));
            } else {
              processInfo.original_rebate_ratio = 0;
            }
          }
        }
      }
    });

    // 如果所有达人都完成了采集，清除定时器
    if (completedCount === res.data.length) {
      if (intervalId.value) {
        clearInterval(intervalId.value);
        intervalId.value = null;
      }
      
      // 同步到kolList
      syncKolList();
      
      // 重新计算总毛利率
      calculateTotalGrossMargin();
    }

    if (showMessage) {
      ElMessage({
        message: "正在采集中",
        type: "success",
        duration: 5000
      });
    }
  } catch (error) {
    if (intervalId.value) {
      clearInterval(intervalId.value);
      intervalId.value = null;
    }
  }
};

// 辅助函数：计算达人价格
const calculateKolPrice = (kol, cooperationType, kolNum) => {
  const platformType = taskDetail.value?.task?.platform_type || 1;
  
  // 首先尝试使用现有kol_price作为基础
  let basePrice = kol.kol_price || 0;
  
  // 根据平台类型和合作形式计算价格
  if (platformType === 1 || platformType === 2) {
    // 抖音平台 - 如果有特定时长价格，则使用
    switch (cooperationType) {
      case 1: return kol.price_1_20 ? (kol.price_1_20 * kolNum) : (basePrice * kolNum);
      case 2: return kol.price_20_60 ? (kol.price_20_60 * kolNum) : (basePrice * kolNum);
      case 71: return kol.price_60 ? (kol.price_60 * kolNum) : (basePrice * kolNum);
      default: return basePrice * kolNum;
    }
  } else if (platformType === 3) {
    // 小红书平台 - 保留原价，只根据数量调整
    return basePrice * kolNum;
  } else if (platformType === 6) {
    // 腾讯互选平台 - 保留原价，只根据数量调整
    return basePrice * kolNum;
  }
  
  // 默认行为 - 保留原价，只根据数量调整
  return basePrice * kolNum;
};

const submitButtonLoadding = ref(false);
// Add this helper function for date formatting
const formatDateForServer = (dateString) => {
  if (!dateString) return null;
  try {
    // Parse the ISO date string
    const date = new Date(dateString);
    // Format as YYYY-MM-DD HH:MM:SS
    return date.getFullYear() + '-' + 
           String(date.getMonth() + 1).padStart(2, '0') + '-' + 
           String(date.getDate()).padStart(2, '0') + ' ' + 
           String(date.getHours()).padStart(2, '0') + ':' + 
           String(date.getMinutes()).padStart(2, '0') + ':' + 
           String(date.getSeconds()).padStart(2, '0');
  } catch (e) {
    return null;
  }
};

// 构建招募任务信息
const buildRecruitmentTaskInfo = () => {
  // 获取平台类型和任务类型，处理可能的字符串类型
  const platformType = Number(taskDetail.value?.task?.platform_type);
  const taskType = Number(taskDetail.value?.task?.task_type);

  // 只有在抖音平台且招募模式下才构建招募任务信息
  if (!((platformType === 1 || platformType === 2) && taskType === 2)) {
    return null;
  }

  const recruitmentTaskInfo = {
    // 总利润信息 - 使用默认值确保类型正确
    assess_total_budget: recruitmentForm.value.assess_total_budget || 0, // float
    expect_coop_num: recruitmentForm.value.expect_coop_num || 0, // int
    assess_total_gross: recruitmentForm.value.assess_total_gross || 0, // float
    assess_total_gross_rate: recruitmentForm.value.assess_total_gross_rate || 0, // float

    // 结算方式 - 使用默认值确保类型正确
    settlement_method: recruitmentForm.value.settlement_method || 0, // int
    recruit_type: recruitmentForm.value.recruit_type || '', // str
    selected_kol_type: recruitmentForm.value.selected_kol_type || '', // str
    bottom_mode: recruitmentForm.value.bottom_mode || '', // str
    assess_indicator: recruitmentForm.value.assess_indicator || '', // str
    kol_bottom_min_price: String(recruitmentForm.value.kol_bottom_min_price || 0), // str
    kol_bottom_max_price: String(recruitmentForm.value.kol_bottom_max_price || 0), // str
    kol_min_price: String(recruitmentForm.value.kol_min_price || 0), // str
    kol_max_price: String(recruitmentForm.value.kol_max_price || 0), // str
    cpm_price: String(recruitmentForm.value.cpm_price || 0), // str
    dou_is_cal_price: recruitmentForm.value.dou_is_cal_price || '', // str
    service_select_mode: recruitmentForm.value.service_select_mode || '', // str
    service_rebate_rate: String(recruitmentForm.value.service_rebate_rate || 0), // str
    enroll_date: recruitmentForm.value.enroll_date ? JSON.stringify(recruitmentForm.value.enroll_date) : '', // str

    // 任务要求 - 使用默认值确保类型正确
    lens_requirement: recruitmentForm.value.lens_requirement || '', // str
    reference_material: recruitmentForm.value.reference_material || '', // str (URL字符串)
    task_enroll_date: recruitmentForm.value.task_enroll_date ? JSON.stringify(recruitmentForm.value.task_enroll_date) : '', // str
    expect_save_time: recruitmentForm.value.expect_save_time || 0, // int

    // 任务信息 - 使用默认值确保类型正确
    kol_task_name: recruitmentForm.value.kol_task_name || '', // str
    task_icon: recruitmentForm.value.task_icon || '', // str (URL字符串)
    xingtu_activity_ip: recruitmentForm.value.xingtu_activity_ip || '', // str
    product_name: recruitmentForm.value.product_name || '', // str
    product_desc: recruitmentForm.value.product_desc || '', // str
    product_link: recruitmentForm.value.product_link || '', // str

    // 组件信息（仅在抖音平台招募任务模式下） - 使用默认值确保类型正确
    is_publish_adv: componentForm.value.is_publish_adv ? JSON.stringify(componentForm.value.is_publish_adv) : '', // str
    effect_adv_id: componentForm.value.effect_adv_id || '', // str
    brand_adv_id: componentForm.value.brand_adv_id || '', // str
    qianchuan_adv_id: componentForm.value.qianchuan_adv_id || '', // str
    dou_adv_id: componentForm.value.dou_adv_id || '', // str
    dou_adv_uid: componentForm.value.dou_adv_uid || '', // str
    component_type: componentForm.value.component_type || 0, // int
    component_content: componentForm.value.component_content || '', // str
    component_url: componentForm.value.component_url || '', // str
    component_remark: componentForm.value.component_remark || '', // str
    extra_remark: componentForm.value.extra_remark || '' // str
  };

  return recruitmentTaskInfo;
};

//提交、保存
const addTaskDataStatusFun = async button_type => {
  // 如果需要审核，先验证审核人员是否都已填写
  // if (isNeedReview.value && button_type == 2) { // 只在提交时验证
  //   if (!validateReviewers()) {
  //     return;
  //   }
  // }

  // 验证招募任务表单（仅在抖音平台且招募模式下）
  const platformType = Number(taskDetail.value?.task?.platform_type);
  const taskType = Number(taskDetail.value?.task?.task_type);
  const isRecruitmentTask = (platformType === 1 || platformType === 2) && taskType === 2;

  if (isRecruitmentTask) {
    try {
      // 验证总利润信息表单
      if (profitFormRef.value) {
        await profitFormRef.value.validate();
      }

      // 验证结算方式表单
      if (settlementFormRef.value) {
        await settlementFormRef.value.validate();
      }

      // 验证任务要求表单
      if (taskRequirementsFormRef.value) {
        await taskRequirementsFormRef.value.validate();
      }

      // 验证任务信息表单
      if (taskInfoFormRef.value) {
        await taskInfoFormRef.value.validate();
      }

      // 验证组件信息表单
      if (componentFormRef.value) {
        await componentFormRef.value.validate();
      }
    } catch (error) {
      console.log('表单验证失败:', error);
      ElMessage({
        message: "请完善招募任务信息后再提交",
        type: "error",
        duration: 5000
      });
      return;
    }
  }

  // Log order_method for debugging

  let kols = [];
  let needReview = true;
  
  // 添加 process_info 完整性校验
  const validateProcessInfo = (item) => {
    if (!item.process_info) {
      ElMessage({
        message: `${item.kol_name}的下单信息未填写完整`,
        type: "error",
        duration: 5000
      });
      return false;
    }

    const requiredFields = [
      'predict_receivable_medium_ratio',
      'predict_receivable_medium_price',
      'predict_cost',
      'gross_profit',
      'gross_profit_margin',
      "original_rebate_ratio"
    ];

    for (const field of requiredFields) {
      if (item.process_info[field] === undefined || item.process_info[field] === null) {
        ElMessage({
          message: `${item.kol_name}的${field}信息未填写完整`,
          type: "error",
          duration: 5000
        });
        return false;
      }
    }

    return true;
  };
  // 提交和变更时都需要进行校验
  if (button_type == 2 || button_type == 3) {
    if (!kolList?.value?.length) {
      ElMessage({
        message: "请选择达人！",
        type: "error",
        duration: 5000
      });
      return;
    }

    // 添加 process_info 验证
    for (const item of kolList.value) {
      if (!validateProcessInfo(item)) {
        return;
      }
    }
    kolList.value.map(item => {
      if (needReview == false) {
        return false;
      }
      if (item.collection_status && item.collection_status != 1) {
        needReview = false;
        ElMessage({
          message: "达人信息采集中/采集失败，无法提交！",
          type: "error",
          duration: 5000
        });
        return;
      }
      kols.map(i => {
        if (item.platform_uid == i.platform_uid && item.cooperation_type == i.cooperation_type) {
          needReview = false;
        }
        ElMessage({
          message: "提交有重复达人请检查！",
          type: "error",
          duration: 5000
        });
        return;
      });
      if (item.kol_attributes == 2 && !item.mcnObject?.mcn_short_name) {
        needReview = false;
        ElMessage({
          message: item.kol_name + "请填写机构简称！",
          type: "error",
          duration: 5000
        });
        return false;
      }
      if (!item.contact_information) {
        needReview = false;
        ElMessage({
          message: item.kol_name + "请填写联系方式！",
          type: "error",
          duration: 5000
        });
        return false;
      }

      if (!item.alliance_personnel_id) {
        needReview = false;
        ElMessage({
          message: item.kol_name + "请选择建联媒介！",
          type: "error",
          duration: 5000
        });
        return false;
      }
      if (!item.process_info?.predict_receivable_medium_ratio && !item.process_info?.predict_cost) {
        needReview = false;
        ElMessage({
          message: "请填写" + item.kol_name + "利润信息！",
          type: "error",
          duration: 5000
        });
        return false;
      }
      if (item.history_point_lists?.length > 0) {
        var arr = item.history_point_lists
          .filter(entry => entry && typeof entry.rebate_ratio === 'number') // 确保数据有效
          .sort((a, b) => a.rebate_ratio - b.rebate_ratio);
          var arr1 = item.history_point_lists
          .filter(entry => entry && typeof entry.rebate_ratio === 'number') // 确保数据有效
          .sort((a, b) => b.rebate_ratio - a.rebate_ratio);
      }
      let minValue = arr && arr?.length > 0 ? arr[0].rebate_ratio : 0;
      let maxValue = arr1 && arr1?.length > 0 ? arr1[0].rebate_ratio : 0;
      item.rebate_ratio = maxValue
      // 计算下单总金额
      let starPrice;
      if (item.process_info?.change_status == 1) {
        starPrice = item.process_info?.actual_amount_price;
      } else {
        const platformType = taskDetail.value?.task?.platform_type || 1;
        if (platformType === 5) { // B站平台
          const serviceFeeRate = item.process_info?.bili_service_type === 1 ? 1.07 : 1.05;
          starPrice = (item.kol_price + item.process_info?.kol_licensing_fee) * serviceFeeRate;
        } else { // 其他平台
          starPrice = (item.kol_price + item.process_info?.kol_licensing_fee) * 1.1;
        }
      }

      const predict_receivable_customer_price =
      starPrice -
      item.process_info?.customer_rebate +
      item.process_info?.customer_service_price;
      //预估成本
      const predict_cost =
      starPrice - item.process_info?.predict_receivable_medium_price;

      // if (
      //   ((predict_receivable_customer_price - predict_cost) / predict_receivable_customer_price < 0.15 || item.process_info?.predict_receivable_medium_ratio < maxValue) &&
      //   (review_data.value?.length != 3)
      // ) {
      //   needReview = false;
      //   ElMessage.error(item.kol_name + "达人毛利率小于15%或返点小于历史返点，需要添加审核！");
      //   return false;
      // }
      return isNeedReview;
    });
  }
  if (!taskDetail.value?.task?.id) {
    needReview = false;
    ElMessage({
      message: "请选择任务！",
      type: "error",
      duration: 5000
    });
    return false;
  }
  if (button_type == 1 && !kolList?.value?.length) {
    try {
      let userCheck = await ElMessageBox.confirm("未选择任何达人，是否保存?");
      if (userCheck !== "confirm") return;
    } catch (error) {
      // 如果点击了取消或关闭，ElMessageBox 会触发一个拒绝的 Promise
      return;
    }
  }

  if (needReview) {
    // 检查是否为抖音招募任务
    const platformType = Number(taskDetail.value?.task?.platform_type);
    const taskType = Number(taskDetail.value?.task?.task_type);
    const isDouyinRecruitmentTask = (platformType === 1 || platformType === 2) && taskType === 2;

    kolList.value?.map(item => {
      const formattedOrderInfo = item.order_info ? {
        ...item.order_info,
        expect_publish_month: item.order_info.expect_publish_month || item.order_info.expect_release_time,
        expect_release_time: formatDateForServer(item.order_info.expect_release_time)
      } : {};

      let kolData = {};

      if (isDouyinRecruitmentTask) {
        kolData = {
          // 基础信息
          kol_name: item.kol_name,
          platform_uid: item.platform_uid,
          cooperation_type: 1, // 招募任务固定为1
          kol_price: Number(item.kol_price) || 0,
          rebate_ratio: Number(item.rebate_ratio) || 0,
          kol_num: item.kol_num || 1,
          kol_photo: item.kol_photo || "",
          price_1_20: Number(item.price_1_20) || 0,
          price_20_60: Number(item.price_20_60) || 0,
          price_60: Number(item.price_60) || 0,
          kol_attributes: item.kol_attributes || 1,
          alliance_personnel: item.alliance_personnel || "",
          alliance_personnel_id: item.alliance_personnel_id || "",
          mcnObject: { ...item.mcnObject } || {},
          mcn_name: item.mcn_name || "",
          mcn_short_name: item.mcn_short_name || "",
          mcn_id: item.mcn_id || "",
          contact_information: item.contact_information || "",
          this_rebate_ratio: Number(item.this_rebate_ratio) || 0,
          this_rebate_price: Number(item.this_rebate_price) || 0,
          xingtu_task_name: item.xingtu_task_name || "",
          xingtu_project_name: item.xingtu_project_name || "",
          process_kol_id: item.process_kol_id || 0,

          // 招募任务特定字段（放在根级别）
          is_xingxiao_kol: item.ext?.is_xingxiao_kol || '',
          kol_fans_num: Number(item.kol_fans_num || item.ext?.kol_fans_num) || 0,
          show_customer_fee: Number(item.show_customer_fee || item.process_info?.show_customer_fee || item.ext?.show_customer_fee) || 0,
          customer_rebate_ratio: Number(item.customer_rebate_ratio || item.process_info?.customer_rebate_ratio || item.ext?.customer_rebate_ratio) || 0,
          provider_price_exclue_service_fee: Number(item.provider_price_exclue_service_fee || item.process_info?.provider_price_exclue_service_fee || item.ext?.provider_price_exclue_service_fee) || 0,
          provider_price: Number(item.provider_price || item.process_info?.provider_price || item.ext?.provider_price) || 0,

          // 期望发布月份（招募任务放在根级别）
          expect_publish_month: item.expect_publish_month || item.ext?.expect_publish_month || item.process_info?.expect_publish_month || item.expect_release_time || item.process_info?.expect_release_time || '',

          // 其他字段仍放在process_info中
          process_info: {
            ...(item.process_info || {}),
            kol_base_price: Number(item.process_info?.kol_base_price || item.kol_price) || 0,
            kol_licensing_fee: Number(item.kol_licensing_fee || item.process_info?.kol_licensing_fee) || 0,
            change_status: Number(item.change_status || item.process_info?.change_status) || 0,
            customer_rebate: Number(item.customer_rebate || item.process_info?.customer_rebate) || 0,
            customer_service_price: Number(item.customer_service_price || item.process_info?.customer_service_price) || 0,
            predict_receivable_medium_ratio: Number(item.predict_receivable_medium_ratio || item.process_info?.predict_receivable_medium_ratio) || 0,
            predict_receivable_customer_price: Number(item.predict_receivable_customer_price || item.process_info?.predict_receivable_customer_price) || 0,
            gross_profit: Number(item.gross_profit || item.process_info?.gross_profit) || 0,
            gross_profit_margin: Number(item.gross_profit_margin || item.process_info?.gross_profit_margin) || 0,
            actual_amount_price: Number(item.actual_amount_price || item.process_info?.actual_amount_price) || 0,
            predict_receivable_medium_price: Number(item.predict_receivable_medium_price || item.process_info?.predict_receivable_medium_price) || 0,
            predict_cost: Number(item.predict_cost || item.process_info?.predict_cost) || 0,
            star_price: Number(item.star_price || item.process_info?.star_price) || 0,
            original_rebate_ratio: Number(item.original_rebate_ratio || item.process_info?.original_rebate_ratio) || 0,
            // 保留旧字段以兼容非招募任务
            expect_release_time: item.expect_release_time || item.process_info?.expect_release_time || ''
          }
        };
      } else {
        // 非招募任务：保持原有的数据结构
        kolData = {
          kol_name: item.kol_name,
          platform_uid: item.platform_uid,
          cooperation_type: item.cooperation_type,
          kol_price: Number(item.kol_price) || 0,
          rebate_ratio: Number(item.rebate_ratio) || 0,
          kol_num: item.kol_num || 1,
          kol_photo: item.kol_photo || "",
          price_1_20: Number(item.price_1_20) || 0,
          price_20_60: Number(item.price_20_60) || 0,
          price_60: Number(item.price_60) || 0,
          kol_attributes: item.kol_attributes || 1,
          alliance_personnel: item.alliance_personnel || "",
          alliance_personnel_id: item.alliance_personnel_id || "",
          mcnObject: { ...item.mcnObject } || {},
          mcn_name: item.mcn_name || "",
          mcn_short_name: item.mcn_short_name || "",
          mcn_id: item.mcn_id || "",
          contact_information: item.contact_information || "",
          this_rebate_ratio: Number(item.this_rebate_ratio) || 0,
          this_rebate_price: Number(item.this_rebate_price) || 0,
          xingtu_task_name: item.xingtu_task_name || "",
          xingtu_project_name: item.xingtu_project_name || "",
          process_kol_id: item.process_kol_id || 0,
          process_info: item.process_info
        };

        // Only include order_info if order_method is not 1
        if (taskDetail.value?.task?.order_method !== 1) {
          kolData.order_info = formattedOrderInfo;
        }
      }

      kols.push(kolData);
    });

    // 构建招募任务信息
    const recruitmentTaskInfo = buildRecruitmentTaskInfo();

    let data = {
      button_type: button_type,
      status: button_type == 2 ? 3 : 2,
      project_id: taskDetail.value?.task?.project_id,
      task_id: taskDetail.value?.task?.id,
      task_name: taskDetail.value?.task?.task_name,
      alliance_personnel_id: taskDetail.value?.process_before?.alliance_personnel_id,
      alliance_personnel: taskDetail.value?.process_before?.alliance_personnel,
      information_personnel_id: taskDetail.value?.process_before?.information_personnel_id,
      information_personnel: taskDetail.value?.process_before?.information_personnel,
      customer_code: isCurrentObj.value?.contract_id ? isCurrentObj.value?.contract_id : undefined,
      customer_contract: isCurrentObj.value?.contract_name ? isCurrentObj.value?.contract_name : undefined,
      approval_type_text: isCurrentObj.value?.approval_type_text ? isCurrentObj.value?.approval_type_text : undefined,
      contract_type: isCurrentObj.value?.contract_type ? isCurrentObj.value?.contract_type : undefined,
      media_or_channel: isCurrentObj.value?.media_or_channel ? isCurrentObj.value?.media_or_channel : undefined,
      contract_sign_date: isCurrentObj.value?.contract_sign_date ? isCurrentObj.value?.contract_sign_date : undefined,
      contract_start_date: isCurrentObj.value?.contract_start_date ? isCurrentObj.value?.contract_start_date : undefined,
      contract_end_date: isCurrentObj.value?.contract_end_date ? isCurrentObj.value?.contract_end_date : undefined,
      contract_amount: isCurrentObj.value?.contract_amount ? isCurrentObj.value?.contract_amount : undefined,
      contract_relative_name: isCurrentObj.value?.contract_relative_name ? isCurrentObj.value?.contract_relative_name : undefined,
      company: isCurrentObj.value?.company ? isCurrentObj.value?.company : undefined,
      oa_project_name: isCurrentObj.value?.project_name ? isCurrentObj.value?.project_name : undefined,
      operator: isCurrentObj.value?.operator ? isCurrentObj.value?.operator : undefined,
      department: isCurrentObj.value?.department ? isCurrentObj.value?.department : undefined
    };

    // 如果是招募任务，添加task_info字段
    if (recruitmentTaskInfo) {
      data.task_info = recruitmentTaskInfo;
    }


    if (kols.length > 0) {
      data.kols = kols;
    }
    submitButtonLoadding.value = true;
    addTaskDataStatus(data).then(res => {
      submitButtonLoadding.value = false; // Always reset loading state first
      if (res.code == 990) {
        if (button_type == 1) {
          ElMessage({
            message: "保存成功！",
            type: "success",
            duration: 5000
          });
          sessionStorage.setItem("status", task_status.value);
          sessionStorage.setItem("active", 1);
          closeCurrentTab();
          router.push({
            path: "/business/task",
            query: { key: "4-3" }
          });
        } else {
          ElMessage({
            message: "提交成功！",
            type: "success",
            duration: 5000
          });
          kolList.value.map(item => {
            // 计算下单总金额
            let starPrice;
            if (item.process_info?.change_status == 1) {
              starPrice = item.process_info?.actual_amount_price;
            } else {
              const platformType = taskDetail.value?.task?.platform_type || 1;
              if (platformType === 5) { // B站平台
                const serviceFeeRate = item.process_info?.bili_service_type === 1 ? 1.07 : 1.05;
                starPrice = (item.kol_price + item.process_info?.kol_licensing_fee) * serviceFeeRate;
              } else { // 其他平台
                starPrice = (item.kol_price + item.process_info?.kol_licensing_fee) * 1.1;
              }
            }

            const predict_receivable_customer_price =
            starPrice -
            item.process_info?.customer_rebate +
            item.process_info?.customer_service_price;
            //预估成本
            const predict_cost =
            starPrice - item.process_info?.predict_receivable_medium_price;

            if (
              // (review_data.value.length == 0 || !review_data.value[0].reviewer_personnel_id)
              false
            ) {
              sessionStorage.setItem("status", 6);
              sessionStorage.setItem("active", 1);
              closeCurrentTab();
              router.push({
                path: "/business/task",
                query: { key: "4-3" }
              });
            } else {
              sessionStorage.setItem("status", 3);
              sessionStorage.setItem("active", 1);
              closeCurrentTab();
              router.push({
                path: "/business/task",
                query: { key: "4-3" }
              });
            }
          });
        }
      } else {
        // Handle non-success response
        ElMessage({
          message: res.msg || "保存失败，请重试",
          type: "error",
          duration: 5000
        });
      }
    }).catch(error => {
      submitButtonLoadding.value = false;
    });
  }
};

const isNeedReviewFun = () => {
  isNeedReview.value = kolList.value?.some(item => {
    // 计算下单总金额
    let starPrice;
    if (item.process_info?.change_status == 1) {
      starPrice = item.process_info?.actual_amount_price;
    } else {
      const platformType = taskDetail.value?.task?.platform_type || 1;
      if (platformType === 5) { // B站平台
        const serviceFeeRate = item.process_info?.bili_service_type === 1 ? 1.07 : 1.05;
        starPrice = (item.kol_price + item.process_info?.kol_licensing_fee) * serviceFeeRate;
      } else { // 其他平台
        starPrice = (item.kol_price + item.process_info?.kol_licensing_fee) * 1.1;
      }
    }

    const predict_receivable_customer_price =
    starPrice -
    item.process_info?.customer_rebate +
    item.process_info?.customer_service_price;
    //预估成本
    const predict_cost =
    starPrice - item.process_info?.predict_receivable_medium_price;

    if (item.history_point_lists?.length > 0) {
      var arr = item.history_point_lists
        .filter(entry => entry && typeof entry.rebate_ratio === 'number') // 确保数据有效
        .sort((a, b) => a.rebate_ratio - b.rebate_ratio);
      var arr1 = item.history_point_lists
        .filter(entry => entry && typeof entry.rebate_ratio === 'number') // 确保数据有效
        .sort((a, b) => b.rebate_ratio - a.rebate_ratio);
    }
    let minValue = arr && arr?.length > 0 ? arr[0].rebate_ratio : 0;
    let maxValue = arr1 && arr1?.length > 0 ? arr1[0].rebate_ratio : 0;
    return (
      (predict_receivable_customer_price - predict_cost) / predict_receivable_customer_price <= 0.15  ||
      item.process_info?.predict_receivable_medium_ratio < maxValue ||
      item.process_info == undefined
    );
  });
  
  
  // 如果需要审核，确保显示所有审核人员
  if (isNeedReview.value) {
    // 确保当前人员包含所有三级
    if (!currentPerson.value.includes(1)) {
      currentPerson.value.push(1);
    }
    if (!currentPerson.value.includes(2)) {
      currentPerson.value.push(2);
    }
    if (!currentPerson.value.includes(3)) {
      currentPerson.value.push(3);
    }
    
    // 如果已有审核数据，确保使用现有数据
    // if (taskDetail.value?.review_data && taskDetail.value.review_data.length > 0) {
    //   // 更新 review_data 数组
    //   review_data.value = taskDetail.value.review_data;
      
    //   // 设置各级审核人员
    //   taskDetail.value.review_data.forEach(item => {
    //     if (item.reviewer_level === 1) {
    //       review_data1.value = item;
    //     } else if (item.reviewer_level === 2) {
    //       review_data2.value = item;
    //     } else if (item.reviewer_level === 3) {
    //       review_data3.value = item;
    //     }
    //   });
      
    //   // 设置 active 值
    //   active.value = taskDetail.value.review_data.length;
    // }
  } 
};
watch(
  () => {
    kolList.value?.length;
  },
  () => {
    kolSelectConfirm.value = JSON.parse(JSON.stringify(kolList.value || {}));
    isNeedReviewFun();
    // 计算总毛利率
    calculateTotalGrossMargin();
  },
  { deep: true }
);

watch(
  () => customerUscList.value,
  () => {
    isCurrentObj.value = customerUscList.value.filter(item => item.contract_id == contract_id.value)[0];
  },
  { deep: true }
);
const uploadFildKol = ref([]);

// 新增计算属性，将last_reviews对象转换为排序后的数组
const auditRecords = computed(() => {
  if (!selectionKolList.value.length) return [];
  
  const lastReviews = selectionKolList.value[0]?.log_reviews || {};
  const result = [];
  
  // 转换对象为数组并过滤掉空对象
  Object.keys(lastReviews).forEach(key => {
    if (Object.keys(lastReviews[key]).length > 0) {
      result.push(lastReviews[key]);
    }
  });
  
  // 不再进行前端排序，直接使用后端返回的顺序
  return result;
});

watch(
  socketStore.messages,
  () => {
    console.log(socketStore.messages, "全局连接接收到消息");
    let msg = socketStore.messages[socketStore.messages.length - 1];
    //接收消息通知
    if (msg.event == "progress") {
      percentage.value = +msg.data.progress.toFixed(0);
      uploadFildKol.value.push(msg.data.data.data.data);
    }
    if (msg.event == "progress_end") {
      
      // 处理导入的达人数据并直接添加到列表中
      progressLevel.value = false; // 关闭进度条
      fileList.value = [];
      
      // 处理导入的达人数据
      const importedKols = msg.data.data || [];
      processImportedKols(importedKols);
      
      // 显示导入结果通知
      if (msg.data.data.filter(item => item.status == false).length) {
        ElNotification({
          title: "提示",
          message: h("span", [
            h("span", { style: "color: gray" }, "解析完成，共"),
            h("span", { style: "color: black" }, msg.data.total_rows),
            h("span", { style: "color: gray" }, "条数据，成功"),
            h("span", { style: "color: green" }, msg.data.success_num),
            h("span", { style: "color: gray" }, "条，失败"),
            h("span", { style: "color: red" }, msg.data.total_rows - msg.data.success_num),
            h("span", { style: "color: gray" }, "条，"),
            h("span", { style: "color: gray" }, "解析失败达人ID："),
            ...msg.data.data
              .filter(item => item.status == false)
              .map(item => {
                return h("div", [h("span", { style: "color: red" }, item.data.data.platform_uid)]);
              })
          ]),
          type: "success",
          duration: 0
        });
      } else {
        ElNotification({
          title: "提示",
          message: h("span", [
            h("span", { style: "color: gray" }, "解析完成，共"),
            h("span", { style: "color: black" }, msg.data.total_rows),
            h("span", { style: "color: gray" }, "条数据，成功"),
            h("span", { style: "color: green" }, msg.data.success_num),
            h("span", { style: "color: gray" }, "条，失败"),
            h("span", { style: "color: red" }, msg.data.total_rows - msg.data.success_num),
            h("span", { style: "color: gray" }, "条。")
          ]),
          type: "success",
          duration: 5000
        });
      }
    }
  },
  { deep: true }
);

const routekey = ref("4-3");

const mediaType = ref(5);
const taskStatus = ref();
const btnRoot = [];
const items = JSON.parse(localStorage.getItem("sidebar") || "[]");
items.map(item => {
  if (item.url.includes("/business/task")) {
    if (item.child) {
      item.child.map(i => {
        btnRoot.push(i.url);
      });
    }
  }
});
const parseURLParams = url => {
  // 用于存储解析后的参数和数值
  let params = {};
  // 获取地址栏中的完整 URL
  let fullURL = url || window.location.href;

  // 解析 URL，提取其中的参数和数值
  let queryString = fullURL.split("?")[1];
  if (queryString) {
    let paramPairs = queryString.split("&");
    for (let i = 0; i < paramPairs.length; i++) {
      let pair = paramPairs[i].split("=");
      let paramName = decodeURIComponent(pair[0]);
      let paramValue = decodeURIComponent(pair[1]);
      params[paramName] = paramValue;
    }
  }

  // 返回解析后的参数和数值对象
  return params;
};
onMounted(() => {
  let obj = parseURLParams();
  if (obj.key) {
    routekey.value = obj.key;
    if (obj.key == "1-5" || obj.key == "2-5") {
      mediaType.value = 5;
    } else if (obj.key == "1-106" || obj.key == "2-106") {
      mediaType.value = 106;
    }
  }
  if (obj.task_status) {
    taskStatus.value = obj.task_status;
  }
});

const updateKolList = () => {
  let data = JSON.parse(localStorage.getItem("kolList")) ? JSON.parse(localStorage.getItem("kolList")) : [];
  selectionKolList.value = data;
  return data;
};

onMounted(() => {
  getRoleUsersFun();
  mcnListFun();
  if (route.query.id) {
    taskProcessKolDetailFun(route.query.id);
  } else {
    // 如果没有任务ID，直接关闭loading
    pageLoading.value = false;
    updateKolList();
    // 修复所有达人价格
    fixKolPrices();
    // 初始化过滤列表
    handleTalentSearch();
  }
  if (route.query.page_type) {
    page_type.value = route.query.page_type;
  }
});

watch(
  () => percentage.value,
  () => {
    if (percentage.value == 100) {
      // taskProcessKolDetailFun(route.query.id || taskDetail.value?.task.id || task_id.value);
    }
  }
);

watch(
  [() => selectionKolList.value?.length, () => selectionKolList.value],
  () => {
    // 设置默认选中
    setDefaultSelection(
      selectionKolList.value.map(item => {
        return { id: item.platform_uid, cooperation_type: item.cooperation_type };
      })
    );

    // 清理现有定时器
    if (intervalId.value) {
      clearInterval(intervalId.value);
      intervalId.value = null;
    }

    // 获取所有需要采集的达人
    const collectionStatusList = selectionKolList.value
      .filter(item => item.collection_status === 2)
      .map(item => ({
        platform_uid: item.platform_uid,
        task_id: item.task_id || taskDetail.value?.task?.id,
        project_id: item.project_id || taskDetail.value?.task?.project_id,
        cooperation_type: item.cooperation_type
      }));

    // 只有在有需要采集的达人时才启动新的定时器
    if (collectionStatusList.length > 0) {
      intervalId.value = setInterval(() => {
        nonKolStarFun(collectionStatusList, false);
      }, 15000);
    } else {
      console.log("无需采集的达人，不启动定时器");
    }
  },
  { deep: true }
);

// 组件销毁时清除定时器
onBeforeUnmount(() => {
  if (intervalId.value) {
    clearInterval(intervalId.value);
    intervalId.value = null;
  }

  // 保存当前状态
  if (selectionKolList.value?.length) {
    localStorage.setItem('kolList', JSON.stringify(selectionKolList.value));
  }
});

watch(
  () => selectionKolList.value,
  () => {
    setDefaultSelection(
      kolList.value?.map(item => {
        return { id: item.platform_uid, cooperation_type: item.cooperation_type };
      })
    );
    let collectionStatusList = [];
    selectionKolList.value.map(item => {
      if (item.collection_status == 2) {
        collectionStatusList.push({
          platform_uid: item.platform_uid,
          task_id: item.task_id ? item.task_id : taskDetail.value?.task.id,
          project_id: item.project_id ? item.task_id : taskDetail.value?.task.project_id,
          cooperation_type: item.cooperation_type
        });
      }
    });
  }
);
// 获取已选达人列表
watch(
  () => kolListRef.value?.getSelectionRows(),
  () => {
    kolList.value = kolListRef.value?.getSelectionRows();
    // 在达人选择变化时立即计算总毛利率
    calculateTotalGrossMargin();
  },
  { deep: true }
);

watch(
  () => task_status.value,
  () => {
    if (task_status.value !== 2 && task_status.value !== 4 && task_status.value !== 3 && route.query.page_type !== "info" && route.query.page_type !== "change") {
      ElMessage({
        message: "该任务已处理",
        type: "warning",
        duration: 5000
      });
      closeCurrentTab();
      router.push({
        path: "/business/task",
        query: {
          key: "4-3",
          task_id: route.query.id
        }
      });
    }
  },
  { deep: true }
);

// 添加建联媒介相关功能
const medium = ref([]);

// 查询建联人员
const searchMedium = () => {
  getRoleUsersInformationApi().then(res => {
    medium.value = res.data;
  });
};

// 在 onMounted 钩子中添加
onMounted(() => {
  searchMedium();
});
const loading4 = ref(false);
const history_point_lists = ref([]);
const historyPointDialogVisible = ref(false);
const currentHistoryKol = ref(null);

// 需求类型映射
const demandType = {
  1: "指派",
  2: "招募",
  3: "投稿"
};

// 添加 Popover 相关功能
const handlePopoverBeforeEnter = async e => {
  loading4.value = true;
  await historyReturnPointApi({
    platform_uid: e.platform_uid
  }).then(res => {
    loading4.value = false;
    history_point_lists.value = res.data;
  });
};

// 打开历史返点对话框
const openHistoryPointDialog = async (rowData) => {
  currentHistoryKol.value = rowData;
  loading4.value = true;
  historyPointDialogVisible.value = true;

  try {
    const res = await historyReturnPointApi({
      platform_uid: rowData.platform_uid
    });
    history_point_lists.value = res.data || [];
  } catch (error) {
    console.error('获取历史返点数据失败:', error);
    ElMessage({
      message: '获取历史返点数据失败',
      type: 'error',
      duration: 5000
    });
    history_point_lists.value = [];
  } finally {
    loading4.value = false;
  }
};

// 关闭历史返点对话框
const closeHistoryPointDialog = () => {
  historyPointDialogVisible.value = false;
  currentHistoryKol.value = null;
  history_point_lists.value = [];
};

// 添加验证函数
// const validateReviewers = () => {
//   if (isNeedReview.value) {
//     if (!review_data1.value.reviewer_personnel_id || !review_data2.value.reviewer_personnel_id || !review_data3.value.reviewer_personnel_id) {
//       ElMessage.error('当触发审核条件时，三级审核人员必须都填写');
//       return false;
//     }
//   }
//   return true;
// };


// 计算总毛利率
const calculateTotalGrossMargin = () => {
  if (!kolList.value || kolList.value.length === 0) {
    totalGrossMargin.value = "0%";
    return;
  }
  
  let totalGrossProfit = 0;
  let totalCustomerReceivables = 0;
  
  kolList.value.forEach(item => {
    if (item.process_info) {
      // 获取毛利
      const grossProfit = Number(item.process_info.gross_profit) || 0;
      // 获取预估应收客户款
      const customerReceivable = Number(item.process_info.predict_receivable_customer_price) || 0;
      
      totalGrossProfit += grossProfit;
      totalCustomerReceivables += customerReceivable;
    }
  });
  
  if (totalCustomerReceivables === 0) {
    totalGrossMargin.value = "0%";
    return;
  }
  
  // 计算总毛利率 = 所有达人毛利之和 / 所有达人预估应收客户款之和 * 100%
  const grossMarginRate = (totalGrossProfit / totalCustomerReceivables) * 100;
  totalGrossMargin.value = grossMarginRate.toFixed(2) + "%";
};

// 修改 watch 函数，在 kolList 变化时计算总毛利率
watch(
  () => {
    kolList.value?.length;
  },
  () => {
    kolSelectConfirm.value = JSON.parse(JSON.stringify(kolList.value || {}));
    isNeedReviewFun();
    // 计算总毛利率
    calculateTotalGrossMargin();
  },
  { deep: true }
);


// 在组件挂载时计算初始总毛利率
onMounted(() => {
  searchMedium();
  // 计算初始总毛利率
  calculateTotalGrossMargin();
});

// 添加 handleSelectionChange 函数，在表格选择变化时调用
const handleSelectionChange = (selection) => {
  // 更新 kolList
  kolList.value = selection;
  // 重新计算总毛利率
  calculateTotalGrossMargin();
};

// After all existing watch functions, add a new watch function
watch(
  selectionKolList,
  () => {
    // Check each KOL's mcnObject.mcn_short_name and update kol_attributes
    selectionKolList.value.forEach(kol => {
      if (kol.mcnObject?.mcn_short_name) {
        kol.kol_attributes = 2; // 机构达人
      } else {
        kol.kol_attributes = 1; // 野生达人
      }
    });

    // 更新过滤列表
    handleTalentSearch();
  },
  { deep: true }
);

// 监听搜索关键词变化
watch(
  talentSearchKeyword,
  () => {
    handleTalentSearch();
  }
);

// 处理导入的达人数据函数
const processImportedKols = (importedKols) => {
  if (!importedKols || importedKols.length === 0) return;
  
  // 获取平台类型
  const platformType = taskDetail.value?.task?.platform_type || 1;
  
  
  // 创建一个Set存储现有达人的唯一标识，以便检查重复
  const existingKolIds = new Set(
    selectionKolList.value.map(kol => `${kol.platform_uid}:${kol.cooperation_type}`)
  );
  
  // 处理每个导入的达人数据
  importedKols.forEach(importedKol => {
    // 如果数据有status属性并且为false，说明导入失败，跳过处理
    if (importedKol.status === false) return;
    
    // 取实际的KOL数据
    const kolData = importedKol.status === undefined ? importedKol : importedKol.data?.data;
    if (!kolData) return;
    
    
    // 创建唯一标识，使用冒号分隔，而不是连字符
    const kolId = `${kolData.platform_uid}:${kolData.cooperation_type}`;
    
    // 检查是否已存在
    if (existingKolIds.has(kolId)) {
      console.log(`达人 ${kolData.kol_name || kolData.platform_uid} 已存在，跳过`);
      return;
    }
    
    // 确定是否需要采集 - 只有platform_type为1或2时才需要采集
    const needCollection = platformType === 1 || platformType === 2;
    
    // 创建order_info对象，用于存储下单信息
    const orderInfo = {
      // 确保初始化ext对象
      ext: {}
    };
    
    // 特别处理is_publish_adv - 优先使用根级别的值
    if (kolData.is_publish_adv !== undefined) {
      orderInfo.is_publish_adv = kolData.is_publish_adv || "";
    }
    
    // 提取order_info相关字段
    if (taskDetail.value?.task?.order_method !== 1) {
      // 提取表单中的order_info字段
      const orderInfoFields = [
        'xingtu_project_name', 'expect_release_time', 'expect_publish_month', 'expect_save_days',
        'juliang_effect_adv_id', 'juliang_brand_adv_id',
        'juliang_qianchuan_adv_id', 'dou_adv_id', 'dou_adv_uid',
        'component_type', 'component_content', 'component_url',
        'component_remark', 'extra_remark'
      ];
      
      // 将导入数据中的order_info字段复制到orderInfo对象
      orderInfoFields.forEach(field => {
        if (kolData[field] !== undefined && kolData[field] !== null) {
          orderInfo[field] = kolData[field];
        }
      });
      
      // 处理特殊字段的类型转换
      if (orderInfo.expect_release_time) {
        // 确保日期格式正确
        orderInfo.expect_release_time = orderInfo.expect_release_time;
      }

      if (orderInfo.expect_publish_month) {
        // 确保月份格式正确
        orderInfo.expect_publish_month = orderInfo.expect_publish_month;
      }
      
      if (orderInfo.expect_save_days !== undefined) {
        orderInfo.expect_save_days = Number(orderInfo.expect_save_days) || 0;
      }
      
      // 如果是_publish_adv尚未设置（没有根级别的值），则从kolData.order_info中获取
      if (orderInfo.is_publish_adv === undefined && kolData.order_info && kolData.order_info.is_publish_adv !== undefined) {
        orderInfo.is_publish_adv = kolData.order_info.is_publish_adv || "";
      }
    }
    
    // 特殊处理平台特定的ext数据
    // 处理所有可能的ext数据位置
    const processExtData = (data, target) => {
      // 小红书特定字段名
      const xhsFields = [
        'xhs_type', 'xhs_brand_name', 'xhs_product_name',
        'xhs_order_total_price', 'xhs_publish_date', 'xhs_ex_remark',
        'xhs_bind_spu', 'xhs_product_id'
      ];
      
      // 腾讯互选特定字段名
      const tencentFields = [
        'tencent_number', 'tencent_is_ry_talent', 'tencent_expect_pb_time',
        'tencent_order_total_price', 'tencent_product_name', 'tencent_product_desc',
        'tencent_video_rquirement', 'tencent_outside_video_desc', 'tencent_script_confirm',
        'tencent_promotion_scene', 'tencent_enable_adv_component', 'tencent_component_info',
        'tencent_enable_second_promotion'
      ];
      
      // B站特定字段
      const bilibiliFields = [
        'bili_service_type', 'bili_product_name', 'bili_contact_information',
        'bili_product_requirement', 'bili_notes'
      ];
      
      // 1. 检查直接的ext对象
      if (data.ext) {
        Object.keys(data.ext).forEach(key => {
          target[key] = data.ext[key];
        });
      }
      
      // 2. 检查嵌套的order_info.ext对象
      if (data.order_info && data.order_info.ext) {
        Object.keys(data.order_info.ext).forEach(key => {
          target[key] = data.order_info.ext[key];
        });
      }
      
      // 3. 检查顶级对象中的特定平台字段
      if (platformType === 3) { // 小红书
        xhsFields.forEach(field => {
          if (data[field] !== undefined && data[field] !== null) {
            target[field] = data[field];
          }
        });
      } else if (platformType === 5) { // B站
        bilibiliFields.forEach(field => {
          if (data[field] !== undefined && data[field] !== null) {
            target[field] = data[field];
          }
        });
      } else if (platformType === 6) { // 腾讯互选
        tencentFields.forEach(field => {
          if (data[field] !== undefined && data[field] !== null) {
            target[field] = data[field];
          }
        });
      }
    };
    
    // 处理ext数据
    processExtData(kolData, orderInfo.ext);
    
    // 特殊处理 - 直接添加导入示例中的结构形式
    if (platformType === 3 && !Object.keys(orderInfo.ext).length && kolData.ext) {
      orderInfo.ext = { ...kolData.ext };
    }
    
    // 确保保留原始价格，特别是对于非抖音平台
    let kolPrice = kolData.kol_price || 0;
    
    // 抖音平台 (1或2) 可能需要根据合作类型和时长计算价格
    if ((platformType === 1 || platformType === 2) && kolData.cooperation_type) {
      // 尝试使用特定时长价格，如果有的话
      if (kolData.cooperation_type === 1 && kolData.price_1_20) {
        kolPrice = kolData.price_1_20 * (kolData.kol_num || 1);
      } else if (kolData.cooperation_type === 2 && kolData.price_20_60) {
        kolPrice = kolData.price_20_60 * (kolData.kol_num || 1);
      } else if (kolData.cooperation_type === 71 && kolData.price_60) {
        kolPrice = kolData.price_60 * (kolData.kol_num || 1);
      }
    }
    
    // 构建标准化的达人数据结构
    const newKol = {
      kol_name: kolData.kol_name || "",
      platform_uid: kolData.platform_uid,
      kol_type: kolData.kol_type || 1,
      kol_price: kolPrice, // 使用处理后的价格
      cooperation_type: kolData.cooperation_type || 1,
      xingtu_task_name: kolData.xingtu_task_name || "",
      xingtu_project_name: kolData.xingtu_project_name || "", // 添加项目名称字段
      kol_num: kolData.kol_num || 1,
      kol_photo: kolData.kol_photo || null,
      price_1_20: kolData.price_1_20 || 0,
      price_20_60: kolData.price_20_60 || 0,
      price_60: kolData.price_60 || 0,
      rebate_ratio: 0,
      kol_attributes: 1, // 默认为野生达人
      // last_reviews: {
      //   1: {},
      //   2: {},
      //   3: {}
      // },
      mcnObject: {
        mcn_name: "",
        mcn_short_name: "",
        mcn_id: ""
      },
      mcn_name: "",
      mcn_short_name: "",
      mcn_id: "",
      this_rebate_ratio: 0,
      this_rebate_price: 0,
      contact_information: "",
      // 如果不需要采集，直接设置为完成状态
      collection_status: needCollection ? 2 : 1, 
      
      // 构建process_info对象
      process_info: {
        kol_base_price: kolPrice, // 使用处理后的价格
        kol_licensing_fee: kolData.kol_licensing_fee || 0,
        change_status: kolData.change_status === "是" ? 1 : 0,
        actual_amount_price: kolData.actual_amount_price || 0,
        predict_receivable_medium_price: kolData.predict_receivable_medium_price || 0,
        predict_receivable_medium_ratio: kolData.predict_receivable_medium_ratio || 0, // 使用导入的值
        customer_rebate: kolData.customer_rebate || 0,
        customer_service_price: kolData.customer_service_price || 0,
        special_rebate_amount: kolData.special_rebate_amount || 0,
        other_notes: kolData.other_notes || "",
        gross_profit: 0, // 将计算
        gross_profit_margin: 0, // 将计算
        // 原返点比例字段，将在calculateKolProfitInfo函数中计算
        original_rebate_ratio: kolData.original_rebate_ratio ||
                              kolData.process_info?.original_rebate_ratio ||
                              0, // 如果导入数据中有值则使用，否则将通过公式计算
        // B站服务类型字段处理
        bili_service_type: 1 // 默认为个人UP主服务费
      },
      task_id: kolData.task_id || taskDetail.value?.task?.id || task_id.value,
      project_id: kolData.project_id || taskDetail.value?.task?.project_id
    };
    
    // 特别处理B站服务类型
    if (platformType === 5) {
      // 优先从直接字段获取
      if (kolData.bili_service_type !== undefined) {
        newKol.process_info.bili_service_type = Number(kolData.bili_service_type);
      }
      // 其次从process_info中获取
      else if (kolData.process_info?.bili_service_type !== undefined) {
        newKol.process_info.bili_service_type = Number(kolData.process_info.bili_service_type);
      }
      // 再从order_info.ext中获取
      else if (kolData.order_info?.ext?.bili_service_type !== undefined) {
        newKol.process_info.bili_service_type = Number(kolData.order_info.ext.bili_service_type);
      }
      // 最后从ext中获取
      else if (kolData.ext?.bili_service_type !== undefined) {
        newKol.process_info.bili_service_type = Number(kolData.ext.bili_service_type);
      }
      // 确保是数字类型
      newKol.process_info.bili_service_type = Number(newKol.process_info.bili_service_type) || 1;
    }
    
    // 如果order_method不是1，并且有order_info数据，则添加order_info
    if (taskDetail.value?.task?.order_method !== 1 && Object.keys(orderInfo).length > 0) {
      newKol.order_info = orderInfo;
    } else if (platformType === 3 || platformType === 6) {
      // 对于小红书和腾讯互选平台，即使order_method为1，也需要添加order_info以存储ext字段
      newKol.order_info = orderInfo;
    }
    
    // 计算毛利率相关数据
    const calculateKolProfitInfo = (kol) => {
      if (!kol.process_info) return;

      const processInfo = kol.process_info;
      const kolPrice = Number(kol.kol_price) || 0;
      const kolLicensingFee = Number(processInfo.kol_licensing_fee) || 0;
      const customerRebate = Number(processInfo.customer_rebate) || 0;
      const customerServicePrice = Number(processInfo.customer_service_price) || 0;
      const predictReceivableMediumPrice = Number(processInfo.predict_receivable_medium_price) || 0;

      // 计算实际下单总金额 (下单总金额)
      let actualAmountPrice;
      if (processInfo.change_status === 1) {
        // 如果改价，使用实际下单金额
        actualAmountPrice = Number(processInfo.actual_amount_price) || 0;
      } else {
        // 根据平台类型计算下单总金额
        if (platformType === 5) { // B站平台
          // B站根据服务类型计算：1=个人UP主(7%), 2=签约UP主(5%)
          const serviceFeeRate = processInfo.bili_service_type === 1 ? 1.07 : 1.05;
          actualAmountPrice = (kolPrice + kolLicensingFee) * serviceFeeRate;
        } else { // 其他平台
          // 下单总金额=(达人裸价+达人授权费裸价)*1.1
          actualAmountPrice = (kolPrice + kolLicensingFee) * 1.1;
        }
      }

      // 计算预估应收客户款
      const predictReceivableCustomerPrice = actualAmountPrice - customerRebate + customerServicePrice;

      // 计算预估成本 - 抖音招募任务特殊处理
      let predictCost;
      let grossProfit;
      let grossProfitMargin = 0;

      // 检查是否为抖音招募任务
      const isDouyinRecruitmentTask = (platformType === 1 || platformType === 2) && taskDetail.value?.task?.task_type === 2;

      if (isDouyinRecruitmentTask) {
        // 抖音招募任务：直接使用接口返回的值，不进行计算
        predictCost = Number(kolData.predict_cost || processInfo.predict_cost) || 0;

        // 对于预估应收客户款、毛利、毛利率也使用接口返回的值
        const predictReceivableCustomerPriceV1 = Number(kolData.predict_receivable_customer_price_v1 || kolData.predict_receivable_customer_price) || predictReceivableCustomerPrice;
        const grossProfitV1 = Number(kolData.gross_profit_v1 || kolData.gross_profit) || 0;
        const grossProfitMarginV1 = Number(kolData.gross_profit_margin_v1 || kolData.gross_profit_margin) || 0;

        // 使用接口返回的值
        grossProfit = grossProfitV1;
        grossProfitMargin = grossProfitMarginV1;

        // 更新process_info - 抖音招募任务使用接口返回的值
        processInfo.actual_amount_price = actualAmountPrice;
        processInfo.star_price = actualAmountPrice;
        processInfo.predict_receivable_customer_price = predictReceivableCustomerPriceV1;
        processInfo.predict_cost = predictCost;
        processInfo.gross_profit = grossProfit;
        processInfo.gross_profit_margin = parseFloat(grossProfitMargin.toFixed(2));

        // 为招募任务添加_v1字段
        processInfo.predict_receivable_customer_price_v1 = predictReceivableCustomerPriceV1;
        processInfo.gross_profit_v1 = grossProfit;
        processInfo.gross_profit_margin_v1 = parseFloat(grossProfitMargin.toFixed(2));
      } else {
        // 非抖音招募任务：使用原有的计算逻辑
        predictCost = actualAmountPrice - predictReceivableMediumPrice;
        grossProfit = predictReceivableCustomerPrice - predictCost;

        if (predictReceivableCustomerPrice > 0) {
          grossProfitMargin = (grossProfit / predictReceivableCustomerPrice) * 100;
        }

        // 更新process_info - 非招募任务使用计算值
        processInfo.actual_amount_price = actualAmountPrice;
        processInfo.star_price = actualAmountPrice;
        processInfo.predict_receivable_customer_price = predictReceivableCustomerPrice;
        processInfo.predict_cost = predictCost;
        processInfo.gross_profit = grossProfit;
        processInfo.gross_profit_margin = parseFloat(grossProfitMargin.toFixed(2));
      }

      // 计算预估应收媒体返点比例和价格的同步逻辑
      if (kolPrice > 0) {
        // 如果已经有比例值但没有价格值，基于比例计算价格
        if (processInfo.predict_receivable_medium_ratio > 0 && predictReceivableMediumPrice === 0) {
          processInfo.predict_receivable_medium_price = (processInfo.predict_receivable_medium_ratio / 100) * kolPrice;
        }
        // 如果已经有价格值但没有比例值，基于价格计算比例
        else if (predictReceivableMediumPrice > 0 && processInfo.predict_receivable_medium_ratio === 0) {
          processInfo.predict_receivable_medium_ratio = (predictReceivableMediumPrice / kolPrice) * 100;
        }
        // 如果两者都没有，保持为0
        // 如果两者都有，保持现有值不变
      }

      // 计算原返点比例：(刊例价 - 实际下单价(含服务费)/1.05 + 预估媒体返点金额) / 刊例价 * 100%
      if (kolPrice > 0) {
        const actualOrderPrice = actualAmountPrice / 1.05; // 实际下单价(含服务费)/1.05
        // 使用更新后的预估媒体返点金额
        const updatedPredictReceivableMediumPrice = Number(processInfo.predict_receivable_medium_price) || 0;
        const originalRebateRatio = ((kolPrice - actualOrderPrice + updatedPredictReceivableMediumPrice) / kolPrice) * 100;
        processInfo.original_rebate_ratio = parseFloat(originalRebateRatio.toFixed(2));
      } else {
        processInfo.original_rebate_ratio = 0;
      }
    };

    // 计算新达人的毛利率信息
    calculateKolProfitInfo(newKol);

    // 对于招募任务，将process_info中的关键字段复制到根级别，以便在表单中正确显示
    if (taskDetail.value?.task?.task_type === 2) { // 招募任务
      const fieldsToSync = [
        'change_status',
        'predict_receivable_medium_ratio',
        'customer_rebate',
        'customer_service_price',
        'kol_licensing_fee'
      ];

      fieldsToSync.forEach(field => {
        if (newKol.process_info[field] !== undefined) {
          newKol[field] = newKol.process_info[field];
        }
      });

      // 特殊处理 change_status，统一使用数字值0/1
      if (kolData.change_status !== undefined) {
        // 将字符串值转换为数字值
        if (kolData.change_status === "是") {
          newKol.change_status = 1;
        } else if (kolData.change_status === "否") {
          newKol.change_status = 0;
        } else {
          newKol.change_status = Number(kolData.change_status) || 0;
        }
      }
    }

    // 添加到达人列表
    selectionKolList.value.push(newKol);
    existingKolIds.add(kolId);
    
  });
  
  // 同步到kolList
  syncKolList();
  
  // 添加到kolList进行选择
  // 注意：由于导入的达人状态为采集中，所以还不能被选中，需要等待采集完成
  
  // 只有当平台类型为1或2时才启动采集
  if (platformType === 1 || platformType === 2) {
    // 启动轮询获取最新的达人信息（价格、图片等）
    const collectionStatusList = selectionKolList.value
      .filter(item => item.collection_status === 2)
      .map(item => ({
        platform_uid: item.platform_uid,
        task_id: item.task_id || taskDetail.value?.task?.id,
        project_id: item.project_id || taskDetail.value?.task?.project_id,
        cooperation_type: item.cooperation_type
      }));
    
    if (collectionStatusList.length > 0) {
      // 立即执行一次，不等待定时器
      nonKolStarFun(collectionStatusList, false);
      
      // 清除现有定时器并创建新的
      if (intervalId.value) {
        clearInterval(intervalId.value);
      }
      
      intervalId.value = setInterval(() => {
        nonKolStarFun(collectionStatusList, false);
      }, 15000);
    }
  } else {
    // 对于非采集类型的平台，确保没有正在运行的定时器
    if (intervalId.value) {
      clearInterval(intervalId.value);
      intervalId.value = null;
    }
  }
  
  // 计算总毛利率
  calculateTotalGrossMargin();
  
  // 修复所有达人价格
  fixKolPrices();
};

// 添加一个函数，用于在socket事件后同步kolList数据
const syncKolList = () => {
  // 只选择已完成采集的达人
  const selectableKols = selectionKolList.value.filter(kol => kol.collection_status === 1);
  
  // 检查kolList是否为空，如果为空，直接初始化它
  if (!kolList.value || kolList.value.length === 0) {
    kolList.value = [...selectableKols];
    return;
  }
  
  // 创建一个映射来检查现有的KOL
  const existingKolMap = new Map(
    kolList.value.map(kol => [`${kol.platform_uid}:${kol.cooperation_type}`, kol])
  );
  
  // 遍历可选的KOL，添加新的，更新现有的
  selectableKols.forEach(kol => {
    const key = `${kol.platform_uid}:${kol.cooperation_type}`;
    if (!existingKolMap.has(key)) {
      kolList.value.push(kol);
    }
  });
  
  // 计算总毛利率
  calculateTotalGrossMargin();
};

const addManualKolDialogVisible = ref(false);
const cancelAddManualKol = () => {
  addManualKolDialogVisible.value = false;
};
const confirmAddManualKol = (data) => {
  // 检查是否已存在该达人
  const existingKolIndex = selectionKolList.value.findIndex(
    item => item.platform_uid === data.platform_uid && item.cooperation_type === data.cooperation_type
  );
  
  if (existingKolIndex !== -1) {
    ElMessage({
      message: "该达人已存在",
      type: "warning",
      duration: 5000
    });
    return;
  }
  
  // 获取平台类型
  const platformType = taskDetail.value?.task?.platform_type || 1;
  
  // 确保价格正确设置
  let kolPrice = data.kol_price || 0;
  
  // 对于抖音平台，可能根据合作类型和时长计算价格
  if ((platformType === 1 || platformType === 2) && data.cooperation_type) {
    if (data.cooperation_type === 1 && data.price_1_20) {
      kolPrice = data.price_1_20 * (data.kol_num || 1);
    } else if (data.cooperation_type === 2 && data.price_20_60) {
      kolPrice = data.price_20_60 * (data.kol_num || 1);
    } else if (data.cooperation_type === 71 && data.price_60) {
      kolPrice = data.price_60 * (data.kol_num || 1);
    }
  }
  
  // 创建新KOL对象，确保所有必需字段都正确初始化
  const newKol = {
    // 基础信息
    kol_name: data.kol_name || "",
    platform_uid: data.platform_uid,
    kol_type: data.kol_type || 1,
    
    // 合作类型和价格信息 - 确保合作类型有效
    cooperation_type: data.cooperation_type || 1, // 默认为1-20S视频
    kol_price: kolPrice, // 使用处理后的价格
    xingtu_task_name: data.xingtu_task_name || "",
    xingtu_project_name: data.xingtu_project_name || "", // 确保项目名称字段被包含
    kol_num: data.kol_num || 1,
    
    // 设置对应的价格字段
    price_1_20: data.price_1_20 || 0,
    price_20_60: data.price_20_60 || 0,
    price_60: data.price_60 || 0,
    
    // 建联媒介信息
    alliance_personnel: data.alliance_personnel || "",
    alliance_personnel_id: data.alliance_personnel_id || "",
    
    // MCN机构信息
    kol_attributes: data.kol_attributes || 1, // 1=野生达人，2=机构达人
    mcnObject: {
      mcn_name: data.mcnObject?.mcn_name || "",
      mcn_short_name: data.mcnObject?.mcn_short_name || "",
      mcn_id: data.mcnObject?.mcn_id || ""
    },
    mcn_name: data.mcn_name || "",
    mcn_short_name: data.mcn_short_name || "",
    mcn_id: data.mcn_id || "",
    
    // 返点信息
    rebate_ratio: data.rebate_ratio || 0,
    this_rebate_ratio: data.this_rebate_ratio || 0,
    this_rebate_price: data.this_rebate_price || 0,
    
    // 联系方式
    contact_information: data.contact_information || "",
    
    // 采集状态 - 手动添加直接设为完成
    collection_status: 1,
    
    // 添加kol_photo字段
    kol_photo: data.kol_photo || "",
    
    // 添加process_kol_id字段
    process_kol_id: data.process_kol_id || null,
    
    // 任务关联信息
    task_id: taskDetail.value?.task?.id || task_id.value,
    project_id: taskDetail.value?.task?.project_id,
    
    // 审核信息
    // last_reviews: {
    //   1: {},
    //   2: {},
    //   3: {}
    // },
    
    // 流程信息 - 确保所有必需字段都有值
    process_info: {
      kol_base_price: kolPrice, // 使用处理后的价格
      kol_licensing_fee: data.process_info?.kol_licensing_fee || 0,
      change_status: data.process_info?.change_status || 0,
      actual_amount_price: data.process_info?.actual_amount_price || 0,
      predict_receivable_medium_price: data.process_info?.predict_receivable_medium_price || 0,
      predict_receivable_medium_ratio: data.process_info?.predict_receivable_medium_ratio || 0,
      customer_rebate: data.process_info?.customer_rebate || 0,
      customer_service_price: data.process_info?.customer_service_price || 0,
      special_rebate_amount: data.process_info?.special_rebate_amount || 0,
      other_notes: data.process_info?.other_notes || "",
      gross_profit: data.process_info?.gross_profit || 0,
      gross_profit_margin: data.process_info?.gross_profit_margin || 0,
      original_rebate_ratio: data.process_info?.original_rebate_ratio || 0,
      status: data.process_info?.status || 1,
      updated_at: data.process_info?.updated_at || new Date().getTime(),
      // 添加缺失的字段
      star_price: data.process_info?.star_price || 0,
      predict_cost: data.process_info?.predict_cost || 0,
      predict_receivable_customer_price: data.process_info?.predict_receivable_customer_price || 0,
      img_url: data.process_info?.img_url || "",
      process_kol_id: data.process_info?.process_kol_id || data.process_kol_id || null,
      order_status: data.process_info?.order_status || 0,
      xingtu_task_name: data.process_info?.xingtu_task_name || data.xingtu_task_name || "",
      // B站服务类型处理 - 确保从正确的数据源获取
      bili_service_type: data.process_info?.bili_service_type ||
                        data.bili_service_type ||
                        data.order_info?.ext?.bili_service_type ||
                        data.ext?.bili_service_type ||
                        1 // 默认为个人UP主服务费
    }
  };
  
  // 如果存在order_info，添加到KOL对象中
  if (data.order_info) {
    newKol.order_info = { ...data.order_info };
    
    // 如果根级的is_publish_adv存在，确保复制到order_info中
    if (data.is_publish_adv !== undefined) {
      newKol.order_info.is_publish_adv = data.is_publish_adv || "";
    }
  }
  // 如果不存在order_info但存在根级is_publish_adv，创建order_info对象
  else if (data.is_publish_adv !== undefined) {
    newKol.order_info = {
      is_publish_adv: data.is_publish_adv || "",
      ext: {}
    };
  }
  
  // 将新达人添加到selectionKolList
  selectionKolList.value.push(newKol);
  
  // 将新达人添加到kolList并确保选中
  if (!kolList.value) {
    kolList.value = [];
  }
  kolList.value.push(newKol);
  
  // ElTableV2 不支持 toggleRowSelection，直接更新 selectedRows
  nextTick(() => {
    // 将新添加的KOL添加到选中列表
    if (!selectedRows.value.some(item =>
      item.platform_uid === newKol.platform_uid &&
      item.cooperation_type === newKol.cooperation_type
    )) {
      selectedRows.value.push(newKol);
      handleSelectionChange(selectedRows.value);
    }

    // 再次计算总毛利率，确保数据更新
    calculateTotalGrossMargin();
  });
  
  // 通知用户添加成功
  ElMessage({
    message: "添加成功",
    type: "success",
    duration: 5000
  });
  
  // 关闭对话框
  addManualKolDialogVisible.value = false;
};

// 计算预估总金额
const estimatedTotalAmount = computed(() => {
  if (!taskDetail.value?.kols?.length) return '0.00';
  
  const totalAmount = taskDetail.value.kols.reduce((acc, curr) => {
    // 检查是否有必要的数据
    if (!curr) return acc;
    
    // 获取刊例价
    const kolBasePrice = Number(curr.kol_price || 0);
    // 获取达人授权费
    const kolLicensingFee = Number(curr.process_info?.kol_licensing_fee || 0);
    
    // 根据平台类型计算下单总金额
    let amount;
    const platformType = taskDetail.value?.task?.platform_type || 1;
    if (platformType === 5) { // B站平台
      // B站根据服务类型计算：1=个人UP主(7%), 2=签约UP主(5%)
      const serviceFeeRate = curr.process_info?.bili_service_type === 1 ? 1.07 : 1.05;
      amount = (kolBasePrice + kolLicensingFee) * serviceFeeRate;
    } else { // 其他平台
      // 下单总金额=(达人裸价+达人授权费裸价)*1.1
      amount = (kolBasePrice + kolLicensingFee) * 1.1;
    }
    
    return acc + amount;
  }, 0);
  
  return totalAmount.toFixed(2);
});

// 计算总毛利
const totalProfit = computed(() => {
  if (taskDetail.value?.kols?.length) {
    const totalGrossProfit = taskDetail.value.kols.reduce((sum, talent) => {
      if (!talent.process_info) return sum;
      
      const predictReceivableCustomerPrice = Number(talent.process_info.predict_receivable_customer_price || 0);
      const predictCost = Number(talent.process_info.predict_cost || 0);
      const grossProfit = predictReceivableCustomerPrice - predictCost;
      
      return sum + (grossProfit > 0 ? grossProfit : 0);
    }, 0);
    
    return totalGrossProfit.toFixed(2);
  }
  
  // 方法三：如果没有具体数据但有总金额和总毛利率，则通过公式计算：总毛利 = 总金额 × 总毛利率 ÷ 100%
  const totalAmount = parseFloat(estimatedTotalAmount.value);
  const grossRate = parseFloat(taskDetail.value?.task?.total_gross || 0);
  
  if (totalAmount && grossRate) {
    return (totalAmount * grossRate / 100).toFixed(2);
  }
  
  return '0.00';
});

// 在 script 部分添加 isTextOverflow 方法
const isTextOverflow = (text, maxLength = 20) => {
  if (!text) return false;
  return text.length > maxLength;
};

// 搜索处理函数
const handleTalentSearch = () => {
  if (!talentSearchKeyword.value.trim()) {
    // 如果搜索关键词为空，显示所有达人
    filteredKolList.value = selectionKolList.value;
  } else {
    // 根据关键词过滤达人
    const keyword = talentSearchKeyword.value.toLowerCase().trim();
    filteredKolList.value = selectionKolList.value.filter(kol => {
      // 搜索达人昵称
      const kolName = (kol.kol_name || '').toLowerCase();
      // 搜索星图ID（platform_uid）
      const platformUid = (kol.platform_uid || '').toString().toLowerCase();

      return kolName.includes(keyword) || platformUid.includes(keyword);
    });
  }
};

// 新增函数：确保所有达人价格正确设置
const fixKolPrices = () => {
  if (!selectionKolList.value?.length) return;
  
  // 获取平台类型
  const platformType = taskDetail.value?.task?.platform_type || 1;
  
  selectionKolList.value = selectionKolList.value.map(kol => {
    // 创建一个新对象以避免直接修改原对象
    const updatedKol = { ...kol };
    
    // 对于非抖音平台，确保kol_price被正确保留
    if (platformType !== 1 && platformType !== 2) {
      // 从可能的来源获取价格
      const price = kol.kol_price || kol.process_info?.kol_base_price || 0;
      
      if (price > 0) {
        // 更新kol_price
        updatedKol.kol_price = price;
        
        // 同步更新process_info.kol_base_price
        if (updatedKol.process_info) {
          updatedKol.process_info.kol_base_price = price;
        }
      }
    }
    
    // 特殊处理B站服务类型 - 在平台类型为5(B站)时
    if (platformType === 5) {
      // 确保process_info存在
      if (!updatedKol.process_info) {
        updatedKol.process_info = {};
      }
      
      // 优先从根级别获取bili_service_type
      if (updatedKol.bili_service_type !== undefined) {
        updatedKol.process_info.bili_service_type = Number(updatedKol.bili_service_type);
        // 可以选择在这里删除根级别的字段，但为了兼容性考虑保留
      }
      // 其次从order_info.ext中获取
      else if (updatedKol.order_info?.ext?.bili_service_type !== undefined) {
        updatedKol.process_info.bili_service_type = Number(updatedKol.order_info.ext.bili_service_type);
      }
      // 最后从ext中获取
      else if (updatedKol.ext?.bili_service_type !== undefined) {
        updatedKol.process_info.bili_service_type = Number(updatedKol.ext.bili_service_type);
      }
      // 如果都没有，设置默认值
      else if (updatedKol.process_info.bili_service_type === undefined) {
        updatedKol.process_info.bili_service_type = 1; // 默认为个人UP主服务费
      }
      
      // 确保字段是数字类型
      updatedKol.process_info.bili_service_type = Number(updatedKol.process_info.bili_service_type);
    }
    
    return updatedKol;
  });
  
  // 更新kolList以保持一致
  if (kolList.value?.length) {
    // 创建一个映射以快速查找
    const kolMap = new Map(
      selectionKolList.value.map(kol => [`${kol.platform_uid}-${kol.cooperation_type}`, kol])
    );
    
    // 更新kolList中的每个KOL
    kolList.value = kolList.value.map(kol => {
      const key = `${kol.platform_uid}-${kol.cooperation_type}`;
      return kolMap.has(key) ? { ...kolMap.get(key) } : kol;
    });
  }
};

</script>

<style >
@import "../index.scss";

.el-table th {
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  background-color: #f5f7fa !important;
}
/* 错误行的背景色 */
.red-row {
  background-color: #ffe6e6 !important;
}

/* 左侧边框仅作用于第一列的单元格 */
.red-row > td:first-child {
  position: relative; /* 让伪元素定位生效 */
  background-color: #ffe6e6 !important;
}

.red-row > td:first-child::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #ff5c5c; /* 红色边框 */
}

.warning-row {
  background-color: #fff9f0 !important;
}


/* 悬停效果 */
.el-table .red-row:hover > td {
  background-color: #ffdada !important;
}

.el-table .red-row:hover > td:first-child::before {
  background-color: #ff3d3d; /* 悬停时左侧边框颜色 */
}

/* 选中行的样式 */
.el-table .el-table__row--current.red-row > td {
  background-color: #ffd6d6 !important;
}


/* 警告行的样式 */
.warning-row {
  background-color: #fff9f0 !important;
  position: relative;
  cursor: help; /* 使用问号光标提示用户有信息 */
}

/* 当鼠标悬停时强化视觉效果 */
.warning-row:hover {
  background-color: #ffefd1 !important;
}

/* 为了确保整行都有悬停效果 */
.warning-row > td {
  position: relative;
}

/* 自定义Tooltip样式（可选） */
.el-tooltip__popper.is-warning {
  background-color: #e6a23c;
  color: white;
  font-weight: bold;
}

/* 警告图标样式 */
.warning-icon {
  color: #e6a23c;
  font-size: 16px;
  cursor: help;
}

/* 警告图标悬停效果 */
.warning-icon:hover {
  color: #d4921e;
}

/* 审核记录样式 */
.audit-description {
  padding: 10px;
  font-size: 14px;
  line-height: 1.6;
  color: #666;
}

.audit-description p {
  margin: 5px 0;
}

:deep(.el-step) {
  margin-bottom: 10px;
}

:deep(.el-step__head.is-success) {
  color: #008b7d;
  border-color: #008b7d;
}

:deep(.el-step__head.is-error) {
  color: #f56c6c;
  border-color: #f56c6c;
}

:deep(.el-step__title.is-success) {
  color: #008b7d;
}

:deep(.el-step__title.is-error) {
  color: #f56c6c;
}

#audit-records {
  margin-top: 20px;
}

/* 审核时间样式 */
.audit-time {
  color: #909399;
  font-size: 13px;
  margin-right: 20px;
}

/* 确保标题行能够容纳审核时间 */
:deep(.el-step__title) {
  width: 100%;
}

/* 基础信息样式 */
.basic-info-grid, .financial-info-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  padding: 12px;
  background-color: #fff;
}

.financial-info-grid {
  grid-template-columns: repeat(3, 1fr);
}

.info-item {
  display: flex;
  flex-direction: column;
  padding: 8px 0;
}

.info-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.info-value {
  font-size: 14px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  min-height: 20px;
  line-height: 20px;
}

/* 新的审核记录时间线样式 */
.audit-timeline {
  position: relative;
  padding: 16px 0;
}

.audit-item {
  display: flex;
  margin-bottom: 24px;
  position: relative;
}

.audit-item:last-child {
  margin-bottom: 0;
}

.audit-marker {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 16px;
  min-width: 24px;
}

.audit-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.audit-item-success .audit-icon {
  background-color: #e0f7f4;
  color: #008b7d;
}

.audit-item-error .audit-icon {
  background-color: #fef0f0;
  color: #f56c6c;
}

.audit-line {
  position: absolute;
  top: 24px;
  width: 2px;
  height: calc(100% + 24px);
  background-color: #ebeef5;
  z-index: 1;
}

.audit-content {
  flex: 1;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid #ebeef5;
  transition: all 0.3s;
}

.audit-item-success .audit-content {
  border-left: 3px solid #008b7d;
}

.audit-item-error .audit-content {
  border-left: 3px solid #f56c6c;
}

.audit-content:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.audit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #ebeef5;
}

.audit-reviewer {
  display: flex;
  align-items: center;
}

.reviewer-name {
  font-weight: 500;
  margin-right: 8px;
  color: #303133;
}

.status-tag {
  font-size: 12px;
  padding: 0 6px;
  height: 20px;
  line-height: 18px;
}

.audit-body {
  padding: 12px 16px;
}

.audit-comment {
  margin: 0;
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
  white-space: pre-line;
}

/* 审核记录响应式样式 */
@media (max-width: 768px) {
  .audit-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .audit-time {
    margin-top: 8px;
    font-size: 12px;
  }
  
  .audit-reviewer {
    flex-wrap: wrap;
  }
  
  .reviewer-name {
    margin-bottom: 4px;
    width: 100%;
  }
  
  .audit-content {
    width: 100%;
  }
}

/* 文本溢出显示省略号 */
.truncate-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  display: block;
}

/* 悬浮时可以显示手型光标提示有完整内容 */
.el-tooltip__trigger .truncate-text {
  cursor: pointer;
}

/* 骨架屏样式 */
.basic-info-skeleton {
  padding: 20px;
}

.skeleton-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.skeleton-info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.talent-info-skeleton {
  padding: 20px;
}

.talent-summary-skeleton {
  margin-bottom: 20px;
}

.skeleton-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.skeleton-table-header {
  background-color: #fafafa;
  padding: 12px;
  border-bottom: 1px solid #ebeef5;
}

.skeleton-table-rows {
  padding: 16px;
}

.skeleton-table-row {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.skeleton-table-row:last-child {
  border-bottom: none;
}

.skeleton-table-cell {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 骨架屏动画优化 */
.el-skeleton__item {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: -100% 50%;
  }
}

/* ElTableV2 样式优化 */
.el-table-v2__header-cell {
  text-align: center;
  background-color: #fff;
  color: #606266;
  font-weight: 600;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #ebeef5;
}

.el-table-v2__row-cell {
  text-align: center;
  padding: 0;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.el-table-v2__row {
  border-bottom: 1px solid #ebeef5;
  height: 80px;
}

.el-table-v2__row:hover {
  background-color: #f5f7fa;
}

/* 警告行样式 */
.el-table-v2__row.warning-row {
  background-color: #fdf6ec;
}

.el-table-v2__row.warning-row:hover {
  background-color: #faecd8;
}

.el-table-v2__row.red-row {
  background-color: #fef0f0;
}

.el-table-v2__row.red-row:hover {
  background-color: #fde2e2;
}

.warning-icon {
  color: #e6a23c;
  font-size: 16px;
}

/* 自定义单元格样式 */
.kol-name-cell {
  height: 80px !important;
  min-height: 80px;
}

.operations-cell {
  height: 80px !important;
  min-height: 80px;
}

/* 表格内组件样式调整 */
.el-table-v2 .el-select,
.el-table-v2 .el-input {
  width: 100%;
}

.el-table-v2 .search-div-photo {
  justify-content: flex-start;
  gap: 8px;
}

.el-table-v2 .search-div-photo img {
  border-radius: 4px;
  object-fit: cover;
}

/* 截断文本样式 */
.truncate-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

/* 确保所有单元格内容都有正确的高度 */
.el-table-v2__cell-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 修复固定列的高度问题 */
.el-table-v2__fixed-left .el-table-v2__row-cell,
.el-table-v2__fixed-right .el-table-v2__row-cell {
  height: 80px !important;
  min-height: 80px !important;
}

.el-table-v2__fixed-left .el-table-v2__row,
.el-table-v2__fixed-right .el-table-v2__row {
  height: 80px !important;
  min-height: 80px !important;
}

/* 固定列的单元格内容 */
.el-table-v2__fixed-left .kol-name-cell,
.el-table-v2__fixed-right .operations-cell {
  height: 80px !important;
  min-height: 80px !important;
}

/* 确保固定列的内容正确显示 */
.el-table-v2__fixed-left,
.el-table-v2__fixed-right {
  z-index: 10;
}

.el-table-v2__fixed-left .el-table-v2__cell-content,
.el-table-v2__fixed-right .el-table-v2__cell-content {
  height: 80px !important;
  min-height: 80px !important;
}

/* 历史返点对话框样式 */
.history-point-dialog {
  .kol-info-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;

    .kol-avatar {
      margin-right: 16px;

      img {
        width: 60px;
        height: 60px;
        border-radius: 8px;
        object-fit: cover;
        border: 2px solid #e1e5e9;
      }
    }

    .kol-details {
      h3 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      p {
        margin: 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .history-table-container {
    .video-info {
      .video-link {
        color: #409eff;
        text-decoration: none;
        font-weight: 500;
        display: block;
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 180px;

        &:hover {
          text-decoration: underline;
        }
      }

      .video-time {
        font-size: 12px;
        color: #999;
      }
    }

    .task-info {
      .task-name {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;

        span {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 120px;
          font-weight: 500;
        }
      }

      .task-type-tag {
        flex-shrink: 0;
      }

      .task-id {
        font-size: 12px;
        color: #999;
      }
    }

    .customer-info {
      .account-name {
        font-weight: 500;
        margin-bottom: 4px;
        color: #333;
      }

      .star-id {
        font-size: 12px;
        color: #999;
      }
    }

    .rebate-ratio {
      font-weight: 600;
      color: #67c23a;
      font-size: 16px;
    }
  }

  .empty-data {
    padding: 40px 0;
    text-align: center;
  }
}



/* 对话框内表格样式优化 */
.history-point-dialog .el-table {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 600;
      }
    }
  }

  .el-table__body-wrapper {
    .el-table__row {
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}

/* 招募表单样式 */
.recruitment-form {
  .form-row-single {
    display: flex;
    gap: 20px;
    align-items: flex-start;
    margin-bottom: 20px;

    .form-item-quarter {
      flex: 1;
      margin-bottom: 0;

      .el-form-item__label {
        font-size: 14px;
        color: #606266;
        font-weight: 500;
        white-space: nowrap;
        padding-right: 8px;
      }

      .el-input {
        width: 100%;

        .el-input__wrapper {
          border-radius: 4px;
        }

        .el-input__suffix {
          color: #909399;
          font-size: 12px;
        }
      }

      .el-select {
        width: 100%;
      }

      .el-date-editor {
        width: 100%;
      }

      /* 价格区间输入框样式 */
      .price-range-input {
        display: flex;
        align-items: center;
        width: 100%;

        span {
          color: #909399;
          font-size: 14px;
          flex-shrink: 0;
        }
      }

      /* 全宽表单项样式 */
      &.form-item-full {
        flex: 1;
        width: 100%;
        margin-bottom: 20px;

        .el-form-item__content {
          width: 100%;
        }
      }

      /* 半宽表单项样式 */
      &.form-item-half {
        flex: 0 0 calc(50% - 10px);
      }

      /* 文本域样式 */
      .el-textarea {
        width: 100%;

        .el-textarea__inner {
          min-height: 120px;
          padding: 12px 15px;
          line-height: 1.5;
          border-radius: 6px;
          border: 1px solid #dcdfe6;
          transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

          &:focus {
            border-color: #409eff;
            outline: none;
          }

          &::placeholder {
            color: #c0c4cc;
          }
        }
      }

      /* 上传组件样式 */
      .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;

        &:hover {
          border-color: #409eff;
        }
      }

      /* 上传提示文字 */
      .upload-tip {
        color: #909399;
        font-size: 12px;
        margin-top: 8px;
        line-height: 1.4;
      }
    }
  }
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .recruitment-form .form-row-single {
    flex-wrap: wrap;

    .form-item-quarter {
      flex: 0 0 calc(50% - 10px);
      margin-bottom: 20px;
    }
  }
}

@media (max-width: 768px) {
  .recruitment-form .form-row-single {
    flex-direction: column;

    .form-item-quarter {
      flex: 1;
      width: 100%;
    }
  }
}

/* 搜索框样式 */
.talent-search-container {
  display: flex;
  align-items: center;
  gap: 12px;
  border-radius: 6px;
  margin: 10px 0;
}

.search-result-text {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}
</style>