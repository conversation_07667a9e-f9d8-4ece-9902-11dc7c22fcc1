<template>
  <!-- 嵌入式模式 -->
  <div v-if="prop.embedded && connectionForm" class="embedded-content">
    <!-- 添加调试信息，方便查看数据状态 -->
    <div v-if="false" class="debug-panel">
      <pre>Props: {{ JSON.stringify(prop.processInfo.actual_amount_price, null, 2) }}</pre>
    </div>
    
    <el-form :model="connectionForm" ref="connectionFormRef">
      <el-tabs v-model="activeTab" class="mb-4">
        <!-- 抖音招募任务利润信息 -->
        <el-tab-pane v-if="isDouyinRecruitmentTask" label="利润信息" name="recruitment">
          <div class="embedded-info-grid">
            <!-- 利润信息 -->
            <div class="embedded-info-item">
              <span class="embedded-label">达人裸价（不含服务费）</span>
              <span class="embedded-value">¥{{ Number(connectionForm.kol_base_price || 0).toFixed(2) }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">达人授权费</span>
              <span class="embedded-value">¥{{ Number(connectionForm.kol_licensing_fee || 0).toFixed(2) }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">对客显示含服务费报价</span>
              <span class="embedded-value">¥{{ Number(connectionForm.order_info.ext.show_customer_fee || 0).toFixed(2) }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">是否改价下单</span>
              <span class="embedded-value">{{ connectionForm.change_status == 1 ? '是' : '否' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">服务商下单价（不含服务费）</span>
              <span class="embedded-value">¥{{ Number(connectionForm.order_info.ext.provider_price_exclue_service_fee || 0).toFixed(2) }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">服务商下单价（含服务费）</span>
              <span class="embedded-value">¥{{ Number(connectionForm.order_info.ext.provider_price || 0).toFixed(2) }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">期望发布日期</span>
              <span class="embedded-value">{{ connectionForm.order_info.ext.expect_publish_month || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">预估成本</span>
              <span class="embedded-value">¥{{ Number(prop.processInfo.predict_cost || 0).toFixed(2) }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">预估应收媒体返点比例</span>
              <span class="embedded-value">{{ Number(connectionForm.predict_receivable_medium_ratio || 0).toFixed(2) }}%</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">客户返点比例</span>
              <span class="embedded-value">{{ Number(connectionForm.order_info.ext.customer_rebate_ratio || 0).toFixed(2) }}%</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">客户返佣</span>
              <span class="embedded-value">¥{{ Number(connectionForm.customer_rebate || 0).toFixed(2) }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">客户服务费</span>
              <span class="embedded-value">¥{{ Number(connectionForm.customer_service_price || 0).toFixed(2) }}</span>
            </div>

            <!-- 后端返回的字段 -->
            <div class="embedded-info-item">
              <span class="embedded-label">预估应收客户款</span>
              <span class="embedded-value">¥{{ Number(connectionForm.predict_receivable_customer_price_v1 || 0).toFixed(2) }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">毛利</span>
              <span class="embedded-value">¥{{ Number(connectionForm.gross_profit_v1 || 0).toFixed(2) }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">毛利率</span>
              <span class="embedded-value">{{ Number(connectionForm.gross_profit_margin || 0).toFixed(2) }}%</span>
            </div>
          </div>
        </el-tab-pane>

        <!-- 普通任务利润信息 -->
        <el-tab-pane v-if="!isDouyinRecruitmentTask" label="利润信息" name="profit">
          <div class="embedded-info-grid">
            <div class="embedded-info-item">
              <span class="embedded-label">平台刊例价</span>
              <span class="embedded-value">¥{{ Number(connectionForm.kol_base_price || 0).toFixed(2) }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">达人授权费</span>
              <span class="embedded-value">¥{{ Number(connectionForm.kol_licensing_fee || 0).toFixed(2) }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">下单总金额</span>
              <span class="embedded-value">¥{{ totalOrderAmount }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">是否改价</span>
              <span class="embedded-value">{{ connectionForm.change_status === 1 ? '是' : '否' }}</span>
            </div>
            <div class="embedded-info-item" v-if="isBilibili">
              <span class="embedded-label">服务类型</span>
              <span class="embedded-value">{{
                connectionForm.order_info.ext.bili_service_type === 1 ? '个人UP主服务费: 任务金额*7%' :
                connectionForm.order_info.ext.bili_service_type === 2 ? '签约UP主服务费: 任务金额*5%' :
                connectionForm.order_info.ext.bili_service_type || '-'
              }}</span>
            </div>
            <!-- 原返点比例 -->
            <div class="embedded-info-item">
              <span class="embedded-label">原返点比例</span>
              <span class="embedded-value">{{ Number(prop.processInfo.original_rebate_ratio || 0).toFixed(2) }}%</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">实际下单价格</span>
              <span class="embedded-value">¥{{ Number(prop.processInfo.actual_amount_price || 0).toFixed(2) }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">预估应收媒体返点比例</span>
              <span class="embedded-value">{{prop.processInfo.kol_base_price ? Number(prop.processInfo.predict_receivable_medium_price / prop.processInfo.kol_base_price * 100 || 0).toFixed(2) : 0 }}%</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">预估应收媒体返点金额</span>
              <span class="embedded-value">¥{{ Number(prop.processInfo.kol_base_price * (prop.processInfo.predict_receivable_medium_price / prop.processInfo.kol_base_price)?.toFixed(4) || 0).toFixed(2) }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">预估成本</span>
              <span class="embedded-value">¥{{ Number(connectionForm.predict_cost || 0).toFixed(2) }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">客户返佣</span>
              <span class="embedded-value">¥{{ Number(connectionForm.customer_rebate || 0).toFixed(2) }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">客户服务费</span>
              <span class="embedded-value">¥{{ Number(connectionForm.customer_service_price || 0).toFixed(2) }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">预估应收客户款</span>
              <span class="embedded-value">¥{{ Number(connectionForm.predict_receivable_customer_price || 0).toFixed(2) }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">毛利</span>
              <span class="embedded-value">¥{{ Number(connectionForm.gross_profit || 0).toFixed(2) }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">毛利率</span>
              <span class="embedded-value">{{ Number(connectionForm.gross_profit_margin || 0).toFixed(2) }}%</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">特殊返点</span>
              <span class="embedded-value">{{ connectionForm.special_rebate_amount || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">投放广告</span>
              <span class="embedded-value">{{ 
                connectionForm.order_info.is_publish_adv === '1,2' ? '磁力智投，磁力金牛' : 
                connectionForm.order_info.is_publish_adv === '1' ? '磁力智投' : 
                connectionForm.order_info.is_publish_adv === '2' ? '磁力金牛' : 
                connectionForm.order_info.is_publish_adv || '-' 
              }}</span>
            </div>
          </div>
          
          <div v-if="connectionForm.other_notes" class="embedded-notes">
            <div class="embedded-notes-title">备注</div>
            <div class="embedded-notes-content">{{ connectionForm.other_notes }}</div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane v-if="!hideOrderInfoTab" label="下单信息" name="order">
          <div class="embedded-info-grid">
            <div class="embedded-info-item">
              <span class="embedded-label">期望发布时间</span>
              <span class="embedded-value">{{ connectionForm.order_info.expect_release_time || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">期望保留时长</span>
              <span class="embedded-value">{{ connectionForm.order_info.expect_save_days || '0' }} 天</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">巨量广告主ID-效果广告</span>
              <span class="embedded-value">{{ connectionForm.order_info.juliang_effect_adv_id || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">巨量广告主ID-品牌广告</span>
              <span class="embedded-value">{{ connectionForm.order_info.juliang_brand_adv_id || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">巨量千川广告主ID</span>
              <span class="embedded-value">{{prop.processInfo.order_info.juliang_qianchuan_adv_id || '-'}}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">Dou+广告主ID</span>
              <span class="embedded-value">{{ connectionForm.order_info.dou_adv_id || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">Dou+广告主抖音UID</span>
              <span class="embedded-value">{{ connectionForm.order_info.dou_adv_uid || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">组件名称</span>
              <span class="embedded-value">
                {{ connectionForm.order_info.component_type == '1' ? '购物车' : 
                    connectionForm.order_info.component_type == '2' ? '落地页' :
                    connectionForm.order_info.component_type == '3' ? '搜索组件' :
                    connectionForm.order_info.component_type == '4' ? '其他' : '-' }}
              </span>
            </div>
          </div>
          
          <div v-if="connectionForm.order_info.component_content" class="embedded-notes">
            <div class="embedded-notes-title">组件文案</div>
            <div class="embedded-notes-content">{{ connectionForm.order_info.component_content }}</div>
          </div>
          
          <div v-if="connectionForm.order_info.component_url" class="embedded-notes">
            <div class="embedded-notes-title">组件链接</div>
            <div class="embedded-notes-content">{{ connectionForm.order_info.component_url }}</div>
          </div>
          
          <div v-if="connectionForm.order_info.component_remark" class="embedded-notes">
            <div class="embedded-notes-title">组件备注</div>
            <div class="embedded-notes-content">{{ connectionForm.order_info.component_remark }}</div>
          </div>
          
          <div v-if="connectionForm.order_info.extra_remark" class="embedded-notes">
            <div class="embedded-notes-title">其他特殊备注</div>
            <div class="embedded-notes-content">{{ connectionForm.order_info.extra_remark }}</div>
          </div>
        </el-tab-pane>
        
        <!-- 小红书信息标签页 - 嵌入式模式 -->
        <el-tab-pane v-if="isXiaohongshu" label="下单信息" name="xiaohongshu">
          <div class="embedded-info-grid">
            <div class="embedded-info-item">
              <span class="embedded-label">类型</span>
              <span class="embedded-value">{{ connectionForm.order_info.ext.xhs_type || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">报备品牌名</span>
              <span class="embedded-value">{{ connectionForm.order_info.ext.xhs_brand_name || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">产品名称</span>
              <span class="embedded-value">{{ connectionForm.order_info.ext.xhs_product_name || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">下单总金额（含服务费）</span>
              <span class="embedded-value">¥{{ Number(connectionForm.order_info.ext.xhs_order_total_price || 0).toFixed(2) }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">发布日期</span>
              <span class="embedded-value">{{ connectionForm.order_info.ext.xhs_publish_date || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">商品ID</span>
              <span class="embedded-value">{{ connectionForm.order_info.ext.xhs_product_id || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">绑定SPU</span>
              <span class="embedded-value">{{ connectionForm.order_info.ext.xhs_bind_spu || '-' }}</span>
            </div>
          </div>
          
          <div v-if="connectionForm.order_info.ext.xhs_ex_remark" class="embedded-notes">
            <div class="embedded-notes-title">备注</div>
            <div class="embedded-notes-content">{{ connectionForm.order_info.ext.xhs_ex_remark }}</div>
          </div>
        </el-tab-pane>

        <!-- 腾讯互选信息标签页 - 嵌入式模式 -->
        <el-tab-pane v-if="isTencentMutual" label="下单信息" name="tencent">
          <div class="embedded-info-grid">
            <!-- <div class="embedded-info-item">
              <span class="embedded-label">序号</span>
              <span class="embedded-value">{{ connectionForm.order_info.ext.tencent_number || '-' }}</span>
            </div> -->
            <div class="embedded-info-item">
              <span class="embedded-label">是否为如翼R0选号达人</span>
              <span class="embedded-value">{{ connectionForm.order_info.ext.tencent_is_ry_talent || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">期望发布时间</span>
              <span class="embedded-value">{{ connectionForm.order_info.ext.tencent_expect_pb_time || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">推广产品</span>
              <span class="embedded-value">{{ connectionForm.order_info.ext.tencent_product_name || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">产品介绍</span>
              <span class="embedded-value">{{ connectionForm.order_info.ext.tencent_product_desc || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">是否进行脚本确认</span>
              <span class="embedded-value">{{ connectionForm.order_info.ext.tencent_script_confirm || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">推广场景</span>
              <span class="embedded-value">{{ connectionForm.order_info.ext.tencent_promotion_scene || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">是否启用广告组件</span>
              <span class="embedded-value">{{ connectionForm.order_info.ext.tencent_enable_adv_component || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">营销目标</span>
              <span class="embedded-value">{{ 
                connectionForm.order_info.ext.tencent_market_target === 1 ? '品牌曝光' : 
                connectionForm.order_info.ext.tencent_market_target === 2 ? '破圈种草' : 
                connectionForm.order_info.ext.tencent_market_target === 3 ? '行动转化' : 
                connectionForm.order_info.ext.tencent_market_target === 4 ? '商品推广' : 
                connectionForm.order_info.ext.tencent_market_target || '-' 
              }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">推广目标</span>
              <span class="embedded-value">{{ 
                connectionForm.order_info.ext.tencent_promote_target === 1 ? '推广品牌活动' : 
                connectionForm.order_info.ext.tencent_promote_target === 2 ? '推广小店商品' : 
                connectionForm.order_info.ext.tencent_promote_target === 3 ? '推广应用' : 
                connectionForm.order_info.ext.tencent_promote_target === 4 ? '添加商家微信' : 
                connectionForm.order_info.ext.tencent_promote_target === 5 ? '推广小游戏' : 
                connectionForm.order_info.ext.tencent_promote_target === 6 ? '收集销售线索' : 
                connectionForm.order_info.ext.tencent_promote_target === 7 ? '关注公众号' : 
                connectionForm.order_info.ext.tencent_promote_target === 8 ? '关注视频号' : 
                connectionForm.order_info.ext.tencent_promote_target === 9 ? '视频号直播预约' : 
                connectionForm.order_info.ext.tencent_promote_target === 10 ? '联名款红包封面' : 
                connectionForm.order_info.ext.tencent_promote_target || '-' 
              }}</span>
            </div>
          </div>
          
          <div v-if="connectionForm.order_info.ext.tencent_video_rquirement" class="embedded-notes">
            <div class="embedded-notes-title">视频内容要求</div>
            <div class="embedded-notes-content">{{ connectionForm.order_info.ext.tencent_video_rquirement }}</div>
          </div>
          
          <div v-if="connectionForm.order_info.ext.tencent_outside_video_desc" class="embedded-notes">
            <div class="embedded-notes-title">外层视频描述文案</div>
            <div class="embedded-notes-content">{{ connectionForm.order_info.ext.tencent_outside_video_desc }}</div>
          </div>
          
          <div v-if="connectionForm.order_info.ext.tencent_component_info" class="embedded-notes">
            <div class="embedded-notes-title">组件信息</div>
            <div class="embedded-notes-content">{{ connectionForm.order_info.ext.tencent_component_info }}</div>
          </div>
          
          <div v-if="connectionForm.order_info.ext.tencent_enable_second_promotion" class="embedded-notes">
            <div class="embedded-notes-title">二次推广意向</div>
            <div class="embedded-notes-content">{{ connectionForm.order_info.ext.tencent_enable_second_promotion }}</div>
          </div>
        </el-tab-pane>
        
        <!-- B站下单信息标签页 - 嵌入式模式 -->
        <el-tab-pane v-if="isBilibili" label="下单信息" name="bilibili">
          <div class="embedded-info-grid">
            <div class="embedded-info-item">
              <span class="embedded-label">期望发布时间</span>
              <span class="embedded-value">{{ connectionForm.order_info.expect_release_time || '-' }}</span>
            </div>
            <!-- <div class="embedded-info-item">
              <span class="embedded-label">任务类型</span>
              <span class="embedded-value">{{ getBilibiliTaskTypeLabel() }}</span>
            </div>
             -->
            <div class="embedded-info-item">
              <span class="embedded-label">联系方式</span>
              <span class="embedded-value">{{ connectionForm.order_info.ext.bili_contact_information || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">产品名称</span>
              <span class="embedded-value">{{ connectionForm.order_info.ext.bili_product_name || '-' }}</span>
            </div>
          </div>

          <div v-if="connectionForm.order_info.ext.bili_product_requirement" class="embedded-notes">
            <div class="embedded-notes-title">制作要求</div>
            <div class="embedded-notes-content">{{ connectionForm.order_info.ext.bili_product_requirement }}</div>
          </div>

          <div v-if="connectionForm.order_info.ext.bili_notes" class="embedded-notes">
            <div class="embedded-notes-title">备注</div>
            <div class="embedded-notes-content">{{ connectionForm.order_info.ext.bili_notes }}</div>
          </div>
        </el-tab-pane>
        
        <!-- 快手下单信息标签页 - 嵌入式模式 -->
        <el-tab-pane v-if="isKuaishou" label="下单信息" name="kuaishou">
          <div class="embedded-info-grid">
            <div class="embedded-info-item">
              <span class="embedded-label">期望发布时间</span>
              <span class="embedded-value">{{ connectionForm.order_info.expect_release_time || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">期望保留时长</span>
              <span class="embedded-value">{{ connectionForm.order_info.expect_save_days || '-' }} 天</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">投放广告</span>
              <span class="embedded-value">{{ 
                connectionForm.order_info.is_publish_adv === '1,2' ? '磁力智投，磁力金牛' : 
                connectionForm.order_info.is_publish_adv === '1' ? '磁力智投' : 
                connectionForm.order_info.is_publish_adv === '2' ? '磁力金牛' : 
                connectionForm.order_info.is_publish_adv || '-' 
              }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">磁力智投账号ID</span>
              <span class="embedded-value">{{ connectionForm.order_info.dou_adv_id || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">磁力金牛账号ID</span>
              <span class="embedded-value">{{ connectionForm.order_info.dou_adv_uid || '-' }}</span>
            </div>
            <div class="embedded-info-item">
              <span class="embedded-label">组件类型</span>
              <span class="embedded-value">
                {{ connectionForm.order_info.component_type == '1' ? '应用推广' :
                    connectionForm.order_info.component_type == '2' ? '品牌推广' :
                    connectionForm.order_info.component_type == '3' ? '快分销带货' :
                    connectionForm.order_info.component_type == '4' ? '其他' : '-' }}
              </span>
            </div>
          </div>
          
          <div v-if="connectionForm.order_info.component_content" class="embedded-notes">
            <div class="embedded-notes-title">组件名称/信息</div>
            <div class="embedded-notes-content">{{ connectionForm.order_info.component_content }}</div>
          </div>
          
          <div v-if="connectionForm.order_info.component_url" class="embedded-notes">
            <div class="embedded-notes-title">组件-链接</div>
            <div class="embedded-notes-content">{{ connectionForm.order_info.component_url }}</div>
          </div>
          
          <div v-if="connectionForm.order_info.component_remark" class="embedded-notes">
            <div class="embedded-notes-title">组件-备注</div>
            <div class="embedded-notes-content">{{ connectionForm.order_info.component_remark }}</div>
          </div>
          
          <div v-if="connectionForm.order_info.extra_remark" class="embedded-notes">
            <div class="embedded-notes-title">其他特殊备注</div>
            <div class="embedded-notes-content">{{ connectionForm.order_info.extra_remark }}</div>
          </div>
        </el-tab-pane>
        
      </el-tabs>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, watch, defineExpose, onMounted, nextTick } from "vue";
const prop = defineProps(["dialogVisible", "dialogType", "processInfo", "orderType", "rowItem", "orderMethod", "embedded", "platformType", "taskType"]);
const emit = defineEmits(["confirmAddKolInfo", "cancelWrite"]);
const connectionFormRef = ref(null);
const fileList = ref([]);
const activeTab = ref('profit'); // 默认显示利润信息标签页
// 添加计算属性来判断是否隐藏下单信息标签页
const hideOrderInfoTab = computed(() => {
  // 抖音招募任务应该隐藏下单信息标签页
  if (isDouyinRecruitmentTask.value) {
    return true;
  }

  // 只有当platformType为1或2(抖音平台)且orderMethod为2时显示下单信息标签
  const isDouyinPlatform = prop.platformType === 1 || prop.platformType === 2;
  const isOrderMethodCorrect = prop.orderMethod === 2;

  // 返回是否应该隐藏该标签页 - 如果不是抖音平台或orderMethod不是2，则隐藏
  return !isDouyinPlatform || !isOrderMethodCorrect;
});

// 添加计算属性判断是否为小红书平台
const isXiaohongshu = computed(() => {
  return prop.platformType === 3;
});

// Add the isTencentMutual computed property after the isXiaohongshu property
const isTencentMutual = computed(() => {
  return prop.platformType === 6;
});

// Add Kuaishou computed property
const isKuaishou = computed(() => {
  return prop.platformType === 4;
});

// Add Bilibili computed property
const isBilibili = computed(() => {
  return prop.platformType === 5;
});

// 添加抖音招募任务判断
const isDouyinRecruitmentTask = computed(() => {
  return (prop.platformType === 1 || prop.platformType === 2) && prop.taskType === 2;
});

// 获取B站任务类型标签
// const getBilibiliTaskTypeLabel = () => {
//   // 优先使用传入的taskType参数（任务级别的task_type）
//   if (prop.taskType !== undefined && prop.taskType !== null) {
//     switch (Number(prop.taskType)) {
//       case 1: return '京火';
//       case 2: return '花火';
//       default: return '-';
//     }
//   }

//   // 如果没有taskType参数，则使用KOL级别的bili_task_type作为备选
//   const biliTaskType = connectionForm.value?.order_info?.ext?.bili_task_type;
//   if (biliTaskType !== undefined && biliTaskType !== null) {
//     switch (Number(biliTaskType)) {
//       case 1: return '京火';
//       case 2: return '花火';
//       default: return biliTaskType || '-';
//     }
//   }

//   return '-';
// };

// 当下单信息标签页被隐藏且当前活动标签页为order时，切换到profit标签页
watch(hideOrderInfoTab, (newVal) => {
  if (newVal && activeTab.value === 'order') {
    // 如果下单信息标签页被隐藏了，而当前正在查看下单信息，则切换到利润信息标签页
    activeTab.value = 'profit';
  }
});

// 小红书平台特有：自动计算下单总金额（含服务费）= 达人刊例价*1.1
watch(
  [() => connectionForm.value?.kol_base_price, isXiaohongshu],
  ([newKolBasePrice, isXHS]) => {
    if (isXHS && newKolBasePrice) {
      // 确保是数值类型并计算
      const totalPrice = (Number(newKolBasePrice) * 1.1).toFixed(2);
      if (connectionForm.value?.order_info) {
        connectionForm.value.order_info.ext.xhs_order_total_price = totalPrice;
      }
    }
  }
);

// Add Tencent watcher for auto-calculating the total price - similar to Xiaohongshu
watch(
  [() => connectionForm.value?.kol_base_price, isTencentMutual],
  ([newKolBasePrice, isTencent]) => {
    if (isTencent && newKolBasePrice) {
      // 确保是数值类型并计算 - 腾讯互选是刊例价*1.05
      const totalPrice = (Number(newKolBasePrice) * 1.05).toFixed(2);
      if (connectionForm.value?.order_info?.ext) {
        connectionForm.value.order_info.ext.tencent_order_total_price = totalPrice;
      }
    }
  }
);

// 确保组件加载后也执行数据初始化
onMounted(async () => {
  
  // 根据任务类型设置默认标签页
  if (isDouyinRecruitmentTask.value) {
    activeTab.value = 'recruitment';
  } else {
    activeTab.value = 'profit';
  }
  
  if (prop.embedded) {
    if (prop.processInfo || prop.rowItem) {
      await initEmbeddedData();
    }
  }
});

// 优化嵌入式模式的数据初始化逻辑
const initEmbeddedData = async () => {
  
  // 先初始化表单
  initInformation();
  
  // 小红书特定字段
  const xhsFields = [
    'xhs_type',
    'xhs_brand_name',
    'xhs_product_name',
    'xhs_order_total_price',
    'xhs_publish_date',
    'xhs_ex_remark',
    'xhs_bind_spu',
    'xhs_product_id'
  ];
    
  // 快手特定字段 - 这些字段存储在order_info的根级别而非ext中
  const kuaishouFields = [
    'expect_release_time',
    'expect_save_days',
    'is_publish_adv',
    'dou_adv_id',
    'dou_adv_uid',
    'component_type',
    'component_content',
    'component_url',
    'component_remark',
    'extra_remark'
  ];

  // B站特定字段 - 存储在order_info.ext中
  const bilibiliFields = [
    'bili_task_type',
    'bili_service_type',
    'bili_contact_information',
    'bili_product_name',
    'bili_product_requirement',
    'bili_notes'
  ];
  
  // 处理来自rowItem的数据
  if (prop.rowItem) {
    if (prop.rowItem.kol_price) {
      connectionForm.value.kol_base_price = Number(prop.rowItem.kol_price) || 0;
    }

    if (prop.rowItem.this_rebate_ratio) {
      connectionForm.value.predict_receivable_medium_ratio = Number(prop.rowItem.this_rebate_ratio) || 0;
    }

    // 处理抖音招募任务专用字段
    if (isDouyinRecruitmentTask.value) {
      const recruitmentFields = [
        'kol_fans_num',
        'is_xingxiao_kol',
        'kol_licensing_fee',
        'show_customer_fee',
        'change_status',
        'provider_price_exclue_service_fee',
        'provider_price',
        'expect_release_time',
        'predict_receivable_medium_ratio',
        'customer_rebate_ratio',
        'customer_rebate',
        'customer_service_price'
      ];

      recruitmentFields.forEach(field => {
        if (prop.rowItem[field] !== undefined && prop.rowItem[field] !== null) {
          // 数值字段特殊处理
          if (field.includes('price') || field.includes('fee') || field.includes('ratio') || field.includes('rebate')) {
            connectionForm.value[field] = Number(prop.rowItem[field]) || 0;
          } else {
            connectionForm.value[field] = prop.rowItem[field];
          }
        }
      });
    }
    
    // 处理rowItem中的order_info数据
    if (prop.rowItem.order_info) {
      
      // 处理基本字段
      Object.keys(prop.rowItem.order_info).forEach(key => {
        if (key !== 'ext' && !xhsFields.includes(key) && connectionForm.value.order_info.hasOwnProperty(key)) {
          connectionForm.value.order_info[key] = prop.rowItem.order_info[key];
        }
      });
      
      // 特别处理小红书字段，将它们放入ext对象
      xhsFields.forEach(field => {
        if (prop.rowItem.order_info[field] !== undefined && prop.rowItem.order_info[field] !== null) {
          // 对数值型字段特殊处理
          if (field === 'xhs_order_total_price') {
            connectionForm.value.order_info.ext[field] = Number(prop.rowItem.order_info[field]) || 0;
          } else {
            connectionForm.value.order_info.ext[field] = prop.rowItem.order_info[field];
          }
        }
      });
      
      // 处理快手平台字段 - 这些字段直接存储在order_info对象中而不是ext内
      if (isKuaishou.value) {
        kuaishouFields.forEach(field => {
          if (prop.rowItem.order_info[field] !== undefined && prop.rowItem.order_info[field] !== null) {
            connectionForm.value.order_info[field] = prop.rowItem.order_info[field];
          }
        });
      }

      // 处理B站平台字段 - 这些字段存储在order_info.ext中
      if (isBilibili.value) {
        bilibiliFields.forEach(field => {
          if (prop.rowItem.order_info[field] !== undefined && prop.rowItem.order_info[field] !== null) {
            connectionForm.value.order_info.ext[field] = prop.rowItem.order_info[field];
          }
        });
      }
      
      // 如果rowItem.order_info已经有ext对象，直接合并
      if (prop.rowItem.order_info.ext) {
        connectionForm.value.order_info.ext = {
          ...connectionForm.value.order_info.ext,
          ...prop.rowItem.order_info.ext
        };
      }
    }
    
    // 处理rowItem中的process_info数据
    if (prop.rowItem.process_info) {
      const processInfoFields = Object.keys(prop.rowItem.process_info);
      processInfoFields.forEach(field => {
        if (connectionForm.value.hasOwnProperty(field)) {
          connectionForm.value[field] = prop.rowItem.process_info[field];
        }
      });
    }
  }
  
  // 处理来自processInfo的数据
  if (prop.processInfo && Object.keys(prop.processInfo).length > 0) {
    // 处理基本字段
    const standardFields = [
      'actual_amount_price', 'change_status', 'customer_rebate', 'customer_service_price',
      'gross_profit', 'gross_profit_margin', 'kol_base_price', 'kol_licensing_fee',
      'other_notes', 'predict_cost', 'predict_receivable_customer_price',
      'predict_receivable_medium_price', 'predict_receivable_medium_ratio',
      'special_rebate_amount', 'img_url', 'original_rebate_ratio'
    ];

    // 抖音招募任务专用字段
    const recruitmentFields = [
      'kol_fans_num', 'is_xingxiao_kol', 'show_customer_fee',
      'provider_price_exclue_service_fee', 'provider_price', 'expect_release_time',
      'customer_rebate_ratio'
    ];
    
    standardFields.forEach(field => {
      if (prop.processInfo[field] !== undefined && prop.processInfo[field] !== null) {
        // 根据字段类型正确处理值
        if (field.includes('price') || field.includes('cost') || field.includes('ratio') ||
            field === 'kol_base_price' || field === 'kol_licensing_fee' ||
            field === 'customer_rebate' || field === 'customer_service_price') {
          connectionForm.value[field] = Number(prop.processInfo[field]) || 0;
        } else if (field === 'change_status') {
          connectionForm.value[field] = Number(prop.processInfo[field]);
        } else {
          connectionForm.value[field] = prop.processInfo[field];
        }
      }
    });

    // 处理抖音招募任务专用字段
    if (isDouyinRecruitmentTask.value) {
      recruitmentFields.forEach(field => {
        if (prop.processInfo[field] !== undefined && prop.processInfo[field] !== null) {
          // 数值字段特殊处理
          if (field.includes('price') || field.includes('fee') || field.includes('ratio')) {
            connectionForm.value[field] = Number(prop.processInfo[field]) || 0;
          } else {
            connectionForm.value[field] = prop.processInfo[field];
          }
        }
      });
    }
    
    // 处理order_info对象中的字段 - 从processInfo中直接提取order_info字段
    const orderInfoFields = [
      'expect_release_time',
      'expect_save_days',
      'juliang_effect_adv_id',
      'juliang_brand_adv_id',
      'qianchuan_advertiser_id',
      'dou_adv_id',
      'dou_adv_uid',
      'component_type',
      'component_content',
      'component_url',
      'component_remark',
      'extra_remark'
    ];
    
    // 小红书特定字段
    const xhsFields = [
      'xhs_type',
      'xhs_brand_name',
      'xhs_product_name',
      'xhs_order_total_price',
      'xhs_publish_date',
      'xhs_ex_remark',
      'xhs_bind_spu',
      'xhs_product_id'
    ];
    
    // 首先检查processInfo是否直接包含order_info对象
    if (prop.processInfo.order_info) {
      // 处理基本字段
      Object.keys(prop.processInfo.order_info).forEach(key => {
        if (key !== 'ext' && !xhsFields.includes(key) && connectionForm.value.order_info.hasOwnProperty(key)) {
          connectionForm.value.order_info[key] = prop.processInfo.order_info[key];
        }
      });
      
      // 处理小红书字段 - 检查是否已经有ext对象
      if (prop.processInfo.order_info.ext) {
        // 直接合并ext对象
        connectionForm.value.order_info.ext = {
          ...connectionForm.value.order_info.ext,
          ...prop.processInfo.order_info.ext
        };
      } else {
        // 将老式结构中的小红书字段放入ext对象
        xhsFields.forEach(field => {
          if (prop.processInfo.order_info[field] !== undefined && prop.processInfo.order_info[field] !== null) {
            // 对数值型字段特殊处理
            if (field === 'xhs_order_total_price') {
              connectionForm.value.order_info.ext[field] = Number(prop.processInfo.order_info[field]) || 0;
            } else {
              connectionForm.value.order_info.ext[field] = prop.processInfo.order_info[field];
            }
          }
        });
        
        // 处理B站字段
        if (isBilibili.value) {
          bilibiliFields.forEach(field => {
            if (prop.processInfo.order_info[field] !== undefined && prop.processInfo.order_info[field] !== null) {
              connectionForm.value.order_info.ext[field] = prop.processInfo.order_info[field];
            }
          });
        }
      }
      
      // 处理快手字段 - 直接保存在order_info根级别
      if (isKuaishou.value) {
        kuaishouFields.forEach(field => {
          if (prop.processInfo.order_info[field] !== undefined && prop.processInfo.order_info[field] !== null) {
            connectionForm.value.order_info[field] = prop.processInfo.order_info[field];
          }
        });
      }
    } else {
      // 如果没有嵌套的order_info对象，则检查顶级字段
      
      // 处理基本字段
      orderInfoFields.forEach(field => {
        if (prop.processInfo[field] !== undefined && prop.processInfo[field] !== null) {
          connectionForm.value.order_info[field] = prop.processInfo[field];
        }
      });
      
      // 处理小红书字段
      xhsFields.forEach(field => {
        if (prop.processInfo[field] !== undefined && prop.processInfo[field] !== null) {
          // 对数值型字段特殊处理
          if (field === 'xhs_order_total_price') {
            connectionForm.value.order_info.ext[field] = Number(prop.processInfo[field]) || 0;
          } else {
            connectionForm.value.order_info.ext[field] = prop.processInfo[field];
          }
        }
      });
      
      // 处理快手平台字段
      if (isKuaishou.value) {
        kuaishouFields.forEach(field => {
          if (prop.processInfo[field] !== undefined && prop.processInfo[field] !== null) {
            connectionForm.value.order_info[field] = prop.processInfo[field];
          }
        });
      }

      // 处理B站平台字段
      if (isBilibili.value) {
        bilibiliFields.forEach(field => {
          if (prop.processInfo[field] !== undefined && prop.processInfo[field] !== null) {
            connectionForm.value.order_info.ext[field] = prop.processInfo[field];
          }
        });
      }
    }
  }
  
  // 计算媒体返点金额
  if (parseFloat(connectionForm.value.kol_base_price) > 0 && 
      parseFloat(connectionForm.value.predict_receivable_medium_ratio) > 0) {
    connectionForm.value.predict_receivable_medium_price = (
      (parseFloat(connectionForm.value.predict_receivable_medium_ratio) / 100) *
      parseFloat(connectionForm.value.kol_base_price)
    ).toFixed(2);
  }
  
  // 在嵌入式模式下，若未改价，则始终重新计算实际下单价格
  if (prop.embedded && connectionForm.value.change_status === 0) {
    // 等待所有依赖值更新完成
    await nextTick();
    // 使用计算得到的价格，而不是直接使用的价格
    connectionForm.value.actual_amount_price = totalOrderAmount.value;
  } else if (connectionForm.value.change_status === 0) {
    // 非嵌入式模式下，计算星图价格并更新实际下单价格
    const calcStarPrice = ((Number(connectionForm.value.kol_base_price) + 
                            Number(connectionForm.value.kol_licensing_fee)) * 1.05).toFixed(2);
    connectionForm.value.actual_amount_price = calcStarPrice;
  }
  
  // 同步所有计算值到表单
  syncCalculatedValues();
  
  // 强制再次更新DOM，确保数据被正确渲染
  await nextTick();
  // 根据任务类型设置默认标签页
  if (isDouyinRecruitmentTask.value) {
    activeTab.value = 'recruitment';
  } else {
    activeTab.value = 'profit';
  }
};

// 修改嵌入式视图，添加关键值的调试显示
const initInformation = () => {
  connectionForm.value = {
    actual_amount_price: 0,
    star_price: 0,
    change_status: 0,
    customer_rebate: 0,
    customer_service_price: 0,
    gross_profit: 0,
    gross_profit_margin: 0,
    kol_base_price: 0,
    kol_licensing_fee: 0,
    other_notes: "",
    predict_cost: 0,
    predict_receivable_customer_price: 0,
    predict_receivable_medium_price: 0,
    predict_receivable_medium_ratio: 0,
    special_rebate_amount: "",
    img_url: "",
    original_rebate_ratio: 0,

    // 抖音招募任务专用字段
    kol_fans_num: "",
    is_xingxiao_kol: "",
    show_customer_fee: 0,
    provider_price_exclue_service_fee: 0,
    provider_price: 0,
    expect_release_time: "",
    customer_rebate_ratio: 0,
    // 确保初始化order_info对象
    order_info: {
      expect_release_time: "",
      expect_save_days: "",
      juliang_effect_adv_id: "",
      juliang_brand_adv_id: "",
      qianchuan_advertiser_id: "",
      dou_adv_id: "",
      dou_adv_uid: "",
      component_type: "",
      component_content: "",
      component_url: "",
      component_remark: "",
      extra_remark: "",
      is_publish_adv: "", // 快手平台特有：是否投放广告
      // 添加特定字段放入ext对象中
      ext: {
        // 小红书特定字段
        xhs_type: "",
        xhs_brand_name: "",
        xhs_product_name: "",
        xhs_order_total_price: 0,
        xhs_publish_date: "",
        xhs_ex_remark: "",
        xhs_bind_spu: "",
        xhs_product_id: "",
        
        // 腾讯互选特定字段
        tencent_number: "",
        tencent_is_ry_talent: "",
        tencent_expect_pb_time: "",
        tencent_order_total_price: 0,
        tencent_product_name: "",
        tencent_product_desc: "",
        tencent_video_rquirement: "",
        tencent_outside_video_desc: "",
        tencent_script_confirm: "",
        tencent_promotion_scene: "",
        tencent_enable_adv_component: "",
        tencent_component_info: "",
        tencent_enable_second_promotion: "",
        tencent_market_target: "",
        tencent_promote_target: "",
        
        // Bilibili specific fields
        bili_task_type: "",
        bili_service_type: "",
        bili_contact_information: "",
        bili_product_name: "",
        bili_product_requirement: "",
        bili_notes: ""
      }
    }
  };
  
  fileList.value = [];
};

// 更新初始表单数据结构，确保使用正确的字段名
const connectionForm = ref({
  actual_amount_price: 0,
  star_price: 0,
  change_status: 0,
  customer_rebate: 0,
  customer_service_price: 0,
  gross_profit: 0,
  gross_profit_v1: 0,
  gross_profit_margin: 0,
  gross_profit_margin_v1: 0,
  kol_base_price: 0,
  kol_licensing_fee: 0,
  other_notes: "",
  predict_cost: 0,
  predict_receivable_customer_price: 0,
  predict_receivable_customer_price_v1: 0,
  predict_receivable_medium_price: 0,
  predict_receivable_medium_ratio: 0,
  special_rebate_amount: "",
  img_url: "",
  original_rebate_ratio: 0,

  // 抖音招募任务专用字段
  kol_fans_num: "",
  is_xingxiao_kol: "",
  show_customer_fee: 0,
  provider_price_exclue_service_fee: 0,
  provider_price: 0,
  expect_release_time: "",
  customer_rebate_ratio: 0,
  // 确保下单信息字段名与API一致
  order_info: {
    expect_release_time: "",
    expect_save_days: "",
    juliang_effect_adv_id: "",
    juliang_brand_adv_id: "",
    qianchuan_advertiser_id: "",
    dou_adv_id: "",
    dou_adv_uid: "",
    component_type: "",
    component_content: "",
    component_url: "",
    component_remark: "",
    extra_remark: "",
    is_publish_adv: "", // 快手平台特有：是否投放广告
    // 添加特定字段放入ext对象中
    ext: {
      // 小红书特定字段
      xhs_type: "",
      xhs_brand_name: "",
      xhs_product_name: "",
      xhs_order_total_price: 0,
      xhs_publish_date: "",
      xhs_ex_remark: "",
      xhs_bind_spu: "",
      xhs_product_id: "",
      
      // 腾讯互选特定字段
      tencent_number: "",
      tencent_is_ry_talent: "",
      tencent_expect_pb_time: "",
      tencent_order_total_price: 0,
      tencent_product_name: "",
      tencent_product_desc: "",
      tencent_video_rquirement: "",
      tencent_outside_video_desc: "",
      tencent_script_confirm: "",
      tencent_promotion_scene: "",
      tencent_enable_adv_component: "",
      tencent_component_info: "",
      tencent_enable_second_promotion: "",
      tencent_market_target: "",
      tencent_promote_target: "",
      
      // Bilibili specific fields
      bili_task_type: "",
      bili_service_type: "",
      bili_contact_information: "",
      bili_product_name: "",
      bili_product_requirement: "",
      bili_notes: ""
    }
  }
});

const isWalkingOrder = ref(false);

//原返点比例   原返点比例=（刊例价-实际下单价（含服务费）/1.05+预估媒体返点金额）/刊例价*100%
const original_rebate_ratio = computed(() => {
  const kol_base_price = Number(connectionForm.value.kol_base_price) || 0;
  const actual_amount_price = Number(connectionForm.value.actual_amount_price) || 0;
  const predict_receivable_medium_price = Number(connectionForm.value.predict_receivable_medium_price) || 0;
  
  if (kol_base_price === 0) return 0;
  
  const ratio = ((kol_base_price - actual_amount_price / 1.05 + predict_receivable_medium_price) / kol_base_price) * 100;
  return ratio > 0 ? ratio.toFixed(2) : 0;
});

// 监听相关数据变化，更新原返点比例
watch(
  [
    () => connectionForm.value.kol_base_price,
    () => connectionForm.value.actual_amount_price,
    () => connectionForm.value.predict_receivable_medium_price
  ],
  () => {
    connectionForm.value.original_rebate_ratio = original_rebate_ratio.value;
  },
  { deep: true }
);
//预估成本
const predict_cost = computed(() => {
  //预估成本=实际下单价格-预估应收媒体返点
  let actual_amount_price = connectionForm.value.actual_amount_price || "0";
  let predict_receivable_medium_price = connectionForm.value.predict_receivable_medium_price || "0";
  
  // 确保是数值类型
  const cost = Number(actual_amount_price) - Number(predict_receivable_medium_price);
  return cost > 0 ? cost.toFixed(2) : "0.00";
});

//预估应收客户款
const predict_receivable_customer_price = computed(() => {
  //预估应收客户款=实际下单价格-客户返佣+客户服务费
  let actual_amount_price = connectionForm.value.actual_amount_price || "0";
  let customer_rebate = connectionForm.value.customer_rebate || "0";
  let customer_service_price = connectionForm.value.customer_service_price || "0";
  
  // 确保是数值类型
  const cost = Number(actual_amount_price) - Number(customer_rebate) + Number(customer_service_price);
  return cost > 0 ? cost.toFixed(2) : "0.00";
});

//毛利
const gross_profit = computed(() => {
  //毛利=预估应收客户款-预估成本
  let predict_receivable_customer_price_val = predict_receivable_customer_price.value || "0";
  let predict_cost_val = predict_cost.value || "0";
  
  // 确保是数值类型
  const cost = Number(predict_receivable_customer_price_val) - Number(predict_cost_val);
  return cost > 0 ? cost.toFixed(2) : "0.00";
});

//毛利率
const gross_profit_margin = computed(() => {
  //毛利率=毛利/预估应收客户款*100%
  let gross_profit_val = gross_profit.value || "0";
  let predict_receivable_customer_price_val = predict_receivable_customer_price.value || "0";
  
  // 避免除以零
  if (Number(predict_receivable_customer_price_val) > 0 && Number(gross_profit_val) > 0) {
    const ratio = (Number(gross_profit_val) / Number(predict_receivable_customer_price_val)) * 100;
    return ratio.toFixed(2);
  }
  
  return "0.00";
});



//
const star_price = computed(() => {
  let kol_base_price = connectionForm.value.kol_base_price || "0";
  let kol_licensing_fee = connectionForm.value.kol_licensing_fee || "0";
  
  // 确保是数值类型
  return ((Number(kol_base_price) + Number(kol_licensing_fee)) * 1.05).toFixed(2);
});

// 为非嵌入式模式添加watcher
watch(
  star_price,
  (newStarPrice) => {
    if (connectionForm.value && connectionForm.value.change_status === 0 && !prop.embedded) {
      // 使用nextTick来避免同一tick中的连续更新
      nextTick(() => {
        connectionForm.value.actual_amount_price = newStarPrice;
      });
    }
  },
  { immediate: true }
);

// 修改kol_base_price和kol_licensing_fee的监听，避免直接修改actual_amount_price
watch(
  [() => connectionForm.value?.kol_base_price, () => connectionForm.value?.kol_licensing_fee],
  () => {
    // 不在这里直接修改actual_amount_price，依赖star_price watcher来完成更新
  },
  { deep: true }
);
watch(
  () => prop.dialogVisible,
  value => {
    if (value === true) {
      // 初始化表单
      initInformation();
      
      // 设置是否为walking order
      isWalkingOrder.value = prop.orderType === 2;
      
      // When walking order type, default to no price change
      if (isWalkingOrder.value && prop.dialogType !== 'info') {
        connectionForm.value.change_status = 0;
      }
      
      // Always set the first tab (profit) as active
      activeTab.value = 'profit';
      
      // 如果有process_info，加载数据
      if (prop.processInfo && Object.keys(prop.processInfo).length > 0) {
        // 处理利润信息字段 
        for (const key of Object.keys(connectionForm.value)) {
          if (key === 'order_info') continue;
          
          if (prop.processInfo[key] !== undefined && prop.processInfo[key] !== null) {
            connectionForm.value[key] = prop.processInfo[key];
          }
        }
        
        // 处理下单信息字段 - 检查是否有嵌套的order_info对象
        if (prop.processInfo.order_info) {
          // 确保connectionForm.value.order_info存在
          if (!connectionForm.value.order_info) {
            connectionForm.value.order_info = {};
          }
          
          // 复制基本字段
          for (const key in prop.processInfo.order_info) {
            // 跳过ext对象，单独处理
            if (key === 'ext') continue;
            connectionForm.value.order_info[key] = prop.processInfo.order_info[key];
          }
          
          // 处理ext对象
          if (prop.processInfo.order_info.ext) {
            if (!connectionForm.value.order_info.ext) {
              connectionForm.value.order_info.ext = {};
            }
            connectionForm.value.order_info.ext = { 
              ...connectionForm.value.order_info.ext, 
              ...prop.processInfo.order_info.ext 
            };
          }
        } else {
          // 否则，直接检查顶级字段
          const orderInfoFields = [
            'expect_release_time',
            'expect_save_days',
            'juliang_effect_adv_id',
            'juliang_brand_adv_id',
            'qianchuan_advertiser_id',
            'dou_adv_id',
            'dou_adv_uid',
            'component_type',
            'component_content',
            'component_url',
            'component_remark',
            'extra_remark'
          ];
          
          orderInfoFields.forEach(field => {
            if (prop.processInfo[field] !== undefined && prop.processInfo[field] !== null) {
              connectionForm.value.order_info[field] = prop.processInfo[field];
            }
          });
        }
        
        // 处理图片
        if (prop.processInfo.img_url && prop.processInfo.img_url !== "") {
          const list = prop.processInfo.img_url.includes(",") 
            ? prop.processInfo.img_url.split(",") 
            : [prop.processInfo.img_url];
          
          list.forEach(item => {
            fileList.value.push({
              name: "item",
              url: item
            });
          });
        }
      }
      
      // 如果有rowItem，处理额外数据
      if (prop.rowItem) {
        if (prop.rowItem.kol_price) {
          connectionForm.value.kol_base_price = prop.rowItem.kol_price;
        }
        
        if (prop.rowItem.this_rebate_ratio) {
          connectionForm.value.predict_receivable_medium_ratio = prop.rowItem.this_rebate_ratio;
        }
        
        // 处理rowItem中的order_info - 注意处理直接在rowItem上的order_info
        if (prop.rowItem.order_info) {
          // 确保connectionForm.value.order_info存在
          if (!connectionForm.value.order_info) {
            connectionForm.value.order_info = {};
          }
          
          // 复制基本字段
          for (const key in prop.rowItem.order_info) {
            // 跳过ext对象，单独处理
            if (key === 'ext') continue;
            connectionForm.value.order_info[key] = prop.rowItem.order_info[key];
          }
          
          // 处理ext对象
          if (prop.rowItem.order_info.ext) {
            if (!connectionForm.value.order_info.ext) {
              connectionForm.value.order_info.ext = {};
            }
            connectionForm.value.order_info.ext = { 
              ...connectionForm.value.order_info.ext, 
              ...prop.rowItem.order_info.ext 
            };
          }
        }
        
        // 计算返点金额
        if (parseFloat(connectionForm.value.kol_base_price) > 0 && 
            parseFloat(connectionForm.value.predict_receivable_medium_ratio) > 0) {
          connectionForm.value.predict_receivable_medium_price = (
            (parseFloat(connectionForm.value.predict_receivable_medium_ratio) / 100) *
            parseFloat(connectionForm.value.kol_base_price)
          ).toFixed(2);
        }
        
        // 设置实际下单价格，不再使用setTimeout，改为同步操作
        if (connectionForm.value.change_status == 0) {
          // 手动计算星图价格
          const calculatedStarPrice = ((Number(connectionForm.value.kol_base_price) + Number(connectionForm.value.kol_licensing_fee)) * 1.05).toFixed(2);
          connectionForm.value.actual_amount_price = calculatedStarPrice;
        }
      }
      
      // 确保所有计算属性被更新
      setTimeout(() => {
        connectionForm.value = { ...connectionForm.value };
        // 如果是嵌入式模式，确保计算值已同步
        if (prop.embedded) {
          syncCalculatedValues();
        }
      }, 0);
    }
  },
  { deep: true }
);

// 同步所有计算值到表单
const syncCalculatedValues = () => {
  // 手动触发所有计算属性
  const starPriceVal = star_price.value;
  const totalOrderAmountVal = prop.embedded ? totalOrderAmount.value : starPriceVal;
  const predictCostVal = predict_cost.value;
  const predictReceivableCustomerPriceVal = predict_receivable_customer_price.value;
  const grossProfitVal = gross_profit.value;
  const originalRebateRatioVal = original_rebate_ratio.value;
  
  // 然后将它们同步到表单中，确保类型正确
  connectionForm.value.star_price = Number(starPriceVal) || 0;
  // For embedded mode, use the calculated totalOrderAmount instead of star_price for actual_amount_price
  if (prop.embedded && connectionForm.value.change_status === 0) {
    connectionForm.value.actual_amount_price = Number(totalOrderAmountVal) || 0;
  }
  connectionForm.value.predict_cost = Number(predictCostVal) || 0;
  connectionForm.value.predict_receivable_customer_price = Number(predictReceivableCustomerPriceVal) || 0;
  connectionForm.value.predict_receivable_customer_price_v1 = prop.rowItem.process_info.predict_receivable_customer_price_v1 || 0;
  connectionForm.value.gross_profit_v1 = prop.rowItem.process_info.gross_profit_v1 || 0;
  connectionForm.value.gross_profit_margin_v1 = prop.rowItem.process_info.gross_profit_margin_v1 || 0;
  connectionForm.value.gross_profit = Number(grossProfitVal) || 0;
  // connectionForm.value.gross_profit_margin = Number(grossProfitMarginVal) || 0;
  connectionForm.value.original_rebate_ratio = Number(originalRebateRatioVal) || 0;
  
  // Recalculate media rebate amount, ensure using latest values
  connectionForm.value.predict_receivable_medium_price = Number(
    ((Number(connectionForm.value.predict_receivable_medium_ratio) / 100) *
     Number(connectionForm.value.kol_base_price)).toFixed(2)
  );
};

// Calculate order amount based on platform type
const totalOrderAmount = computed(() => {
  const kolBasePrice = Number(connectionForm.value.kol_base_price || 0);
  const kolLicensingFee = Number(connectionForm.value.kol_licensing_fee || 0);
  
  // For Xiaohongshu (platformType === 3), use 1.1 multiplier
  if (isXiaohongshu.value) {
    return ((kolBasePrice + kolLicensingFee) * 1.1).toFixed(2);
  }
  
  // For Tencent (platformType === 6), use 1.05 multiplier
  if (isTencentMutual.value) {
    return ((kolBasePrice + kolLicensingFee) * 1.05).toFixed(2);
  }
  
  // For all other platforms, use 1.05 multiplier
  return ((kolBasePrice + kolLicensingFee) * 1.05).toFixed(2);
});

// Watch for changes in totalOrderAmount in embedded mode to update actual_amount_price
watch(
  totalOrderAmount,
  (newTotalAmount) => {
    if (prop.embedded && connectionForm.value && connectionForm.value.change_status === 0) {
      // In embedded mode, ensure actual_amount_price is calculated, not directly used
      nextTick(() => {
        connectionForm.value.actual_amount_price = newTotalAmount;
      });
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.float-right {
  float: right;
}

.tag-multiple {
  background-color: rgba(170, 63, 200, 0.2);
  color: #666;
  padding: 2px 8px;
  margin-right: 10px;
  border-radius: 10px;
  font-size: 14px;
  cursor: pointer;
}

.w-full {
  width: 100% !important;
}

.embedded-content {
  background-color: #fff;
  border-radius: 4px;
  padding: 15px;
}

.embedded-info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 16px;
}

.embedded-info-item {
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
  padding: 10px;
  border-radius: 4px;
}

.embedded-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.embedded-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.embedded-notes {
  margin-top: 16px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #eee;
}

.embedded-notes-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #606266;
}

.embedded-notes-content {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  white-space: pre-wrap;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
}

@media (max-width: 1200px) {
  .embedded-info-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .embedded-info-grid {
    grid-template-columns: 1fr;
  }
}
</style>
